# Azure OpenAI API Configuration
AZURE_OPENAI_API_KEY = 'DJ2Gpu5cCF3hNC45S46RXXASl8taegMDWHnkF5GjSMhNQTrkUOzbJQQJ99BEACfhMk5XJ3w3AAABACOGbdte'
ENDPOINT_URL = 'https://nerllm.openai.azure.com'
DEPLOYMENT_NAME = 'gpt-4.1'
API_VERSION = '2025-01-01-preview'

# Build Azure OpenAI API URL
API_URL = f"{ENDPOINT_URL}/openai/deployments/{DEPLOYMENT_NAME}/chat/completions?api-version={API_VERSION}"




# GPT-4o Azure OpenAI API 配置
AZURE_OPENAI_API_KEY = os.getenv('AZURE_OPENAI_API_KEY_4O', '1ZVPYTWJTBRce59D4W6hYCeTiIsBfwSgQzaqFCQqtUV3ldrCZRIvJQQJ99BEACYeBjFXJ3w3AAABACOG7uxm')
ENDPOINT_URL = os.getenv('ENDPOINT_URL_4O', 'https://nerllm-4o.openai.azure.com/')
DEPLOYMENT_NAME = os.getenv('DEPLOYMENT_NAME_4O', 'gpt-4o')
API_VERSION = os.getenv('API_VERSION_4O', '2025-01-01-preview')

# 配置API密钥
API_KEY = os.getenv("AZURE_OPENAI_API_KEY_4O", AZURE_OPENAI_API_KEY)
# 从环境变量读取模拟模式设置，默认不使用模拟模式
USE_MOCK_API = os.getenv("USE_MOCK_API", "false").lower() == "true"

# Initialize Azure OpenAI client
client = None
if API_KEY and not USE_MOCK_API:
    try:
        client = AzureOpenAI(
            api_key=API_KEY,
            api_version=API_VERSION,
            azure_endpoint=ENDPOINT_URL
        )
        print("Azure OpenAI client initialized successfully")
    except Exception as e:
        print(f"Error initializing Azure OpenAI client: {e}")
        print("Falling back to mock mode")
        USE_MOCK_API = True




