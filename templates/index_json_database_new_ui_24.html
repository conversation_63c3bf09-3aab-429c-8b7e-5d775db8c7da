<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG-CHATBOT</title>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://kit.fontawesome.com/8e9c71f3b7.js" crossorigin="anonymous"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://alcdn.msauth.net/browser/2.19.0/js/msal-browser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

    <style>
        /* Modern Black & White Theme with Responsive Design */
        :root {
            --primary-color: #000000;
            --secondary-color: #1A1A1A;
            --background-color: #FFFFFF;
            --light-gray: #F8F8F8;
            --medium-gray: #E5E5E5;
            --dark-gray: #666666;
            --accent-color: #000000;
            --text-color: #1A1A1A;
            --border-radius: 16px;
            --small-radius: 12px;
            --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            --transition: all 0.2s ease;
            --font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            
            /* Responsive width variables */
            --content-width: 85vw;
            --content-max-width: 1400px;
            --content-min-width: 320px;
            --content-padding: 2vw;
        }

        /* Responsive breakpoints */
        @media (max-width: 768px) {
            :root {
                --content-width: 95vw;
                --content-padding: 1vw;
            }
        }

        @media (min-width: 1920px) {
            :root {
                --content-width: 75vw;
                --content-max-width: 1600px;
            }
        }

        @media (min-width: 2560px) {
            :root {
                --content-width: 65vw;
                --content-max-width: 1800px;
            }
        }

        body {
            font-family: var(--font-family);
            color: var(--text-color);
            background-color: var(--background-color);
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
        }

        /* Updated responsive chat form */
        .chat-form {
            display: flex;
            flex-direction: column;
            align-items: stretch;
            width: var(--content-width);
            max-width: var(--content-max-width);
            min-width: var(--content-min-width);
            margin: 0 auto;
            padding: 0 var(--content-padding);
        }

        /* Updated responsive chat window */
        .chat-window {
            height: calc(100% - 80px - 140px);
            padding: 0 var(--content-padding);
            background-color: transparent;
            scroll-behavior: smooth;
            font-family: var(--font-family);
            width: var(--content-width);
            max-width: var(--content-max-width);
            min-width: var(--content-min-width);
            margin-left: auto;
            margin-right: auto;
        }

        /* Responsive input container */
        .input-container {
            position: relative;
            background: var(--light-gray);
            border-radius: 24px;
            border: 1px solid var(--medium-gray);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 12px 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transition: var(--transition);
            width: 100%;
            max-width: min(var(--content-max-width), 80vw);
            min-width: var(--content-min-width);
            margin: 0 auto;
        }

        /* Mobile-specific adjustments */
        @media (max-width: 768px) {
            .chat-window {
                padding: 0 16px;
            }
            
            .input-area {
                padding: 16px;
            }
            
            .input-container {
                min-width: 280px;
                max-width: 95vw;
            }
            
            .dropdown-content {
                width: calc(100vw - 32px);
                max-width: 380px;
                left: 50%;
                transform: translateX(-50%);
            }
        }

        /* Large screen adjustments */
        @media (min-width: 1400px) {
            .chat-window {
                padding: 0 max(32px, var(--content-padding));
            }
        }

        .thinking-part {
            background-color: var(--light-gray);
            color: var(--dark-gray);
            font-size: 15px;
            padding: 20px;
            border-radius: var(--small-radius);
            margin-bottom: 20px;
            border: 1px solid var(--medium-gray);
        }

        .headline-container {
            display: flex;
            justify-content: space-between;
        }

        .filename {
            margin-left: 15px;
            margin-top: 15px;
            display: flex;
            font-weight: 500;
        }

        .page-button {
            background: var(--primary-color);
            border: none;
            margin-left: 10px;
            color: white;
            height: 32px;
            border-radius: 8px;
            font-weight: 500;
            margin-right: 15px;
            transition: var(--transition);
        }

        .page-button:hover {
            background: #333;
        }

        .pdf-button-group {
            display: flex;
            align-items: center;
            justify-content: end;
        }

        #my-pdf {
            height: calc(100% - 110px)
        }

        .cancel-button {
            display: flex;
            justify-content: end;
            align-items: center;
            margin-right: 10px;
            border: none;
            background: transparent;
            transition: var(--transition);
        }

        .cancel-button:hover,
        .cancel-button:focus {
            color: #555;
            background: transparent;
            border: none;
        }

        .cancel-button:active {
            top: .08em;
            border: none;
        }

        .toggle-chat-history-btn {
            position: fixed;
            align-content: center;
            margin-top: 35px;
            transform: translateY(-50%);
            background-color: transparent;
            border: none;
            transition: var(--transition);
        }

        .toggle-chat-history-btn:hover {
            color: #555;
        }

        .toggle-chat-history-btn .fas {
            transition: transform 0.3s;
        }

        .custom-icon-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .icon-btn {
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 20px;
            transition: var(--transition);
        }

        .send-icon {
            border: none;
            width: 40px;
            height: 40px;
            cursor: pointer;
            transition: var(--transition);
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }



        .database-icon {
            left: 10px;
        }

        .chat-history-entry {
            width: 100%;
            border-radius: var(--border-radius);
            margin-bottom: 16px;
            padding: 0;
            background: transparent;
            font-family: var(--font-family);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            cursor: pointer;
            transition: var(--transition);
            border: none;
        }

        .chat-history-entry:hover {
            transform: translateY(-2px);
        }

        .chat-history-content {
            width: 100%;
            background: white;
            border-radius: var(--small-radius);
            border: 1px solid var(--medium-gray);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            transition: var(--transition);
        }

        .chat-history-entry:hover .chat-history-content {
            border-color: var(--dark-gray);
            box-shadow: var(--box-shadow);
        }

        .chat-history-header {
            padding: 16px 20px 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--light-gray);
        }

        .chat-history-time {
            color: var(--dark-gray);
            font-size: 13px;
            font-weight: 600;
            font-family: var(--font-family);
        }

        .chat-history-delete-btn {
            background: transparent;
            border: none;
            padding: 4px;
            cursor: pointer;
            border-radius: 6px;
            transition: var(--transition);
        }

        .chat-history-delete-btn:hover {
            background: var(--medium-gray);
        }

        .delete-icon {
            width: 16px;
            height: 16px;
            opacity: 0.6;
        }

        .chat-history-divider {
            height: 1px;
            background: var(--medium-gray);
            margin: 0 20px;
        }

        .chat-history-body {
            padding: 16px 20px 20px 20px;
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .chat-history-title-label {
            color: var(--text-color);
            font-size: 14px;
            font-weight: 600;
            font-family: var(--font-family);
            white-space: nowrap;
        }

        .chat-history-title-text {
            color: var(--text-color);
            font-size: 14px;
            font-weight: 500;
            font-family: var(--font-family);
            line-height: 1.4;
            word-wrap: break-word;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            flex: 1;
        }

        .loader {
            border: 3px solid var(--medium-gray);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1.5s linear infinite;
        }

        .docu-loader {
            border: 3px solid var(--medium-gray);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 32px;
            height: 32px;
            animation: spin 1.5s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        /* Streaming text cursor animation */
        .cursor-blink {
            animation: blink 1s infinite;
            color: rgba(255, 255, 255, 0.8);
            font-weight: normal;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .streaming-content {
            min-height: 20px;
        }

        /* Modern switch styling */
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--medium-gray);
            transition: .3s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .3s;
            border-radius: 50%;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        input:checked+.slider {
            background-color: var(--primary-color);
        }

        input:focus+.slider {
            box-shadow: 0 0 1px var(--primary-color);
        }

        input:checked+.slider:before {
            transform: translateX(20px);
        }

        /* Button and interactive elements */
        button {
            transition: var(--transition);
        }
        
        button:hover {
            opacity: 0.9;
        }
        
        /* Clean scrollbars */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: var(--light-gray);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--medium-gray);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--dark-gray);
        }

        /* Teal Blue Theme */
        .theme-teal {
            --primary-color: #004F59;
            --secondary-color: #1A1A1A;
            --accent-color: #004F59;
        }

        .theme-teal .page-button:hover {
            background: #006666;
        }

        .theme-teal input:checked+.slider {
            background-color: #004F59;
        }

        .theme-teal input:focus+.slider {
            box-shadow: 0 0 1px #004F59;
        }

        .theme-teal .chat-history-header {
            background: #E0F2F1;
        }

        .sideM {
            position: fixed;
            width: 80px;
            height: 100%;
            background-color: white;
            padding: 20px 0;
            border-right: 1px solid var(--medium-gray);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
        }

        .main-win {
            position: fixed;
            width: calc(100% - 80px);
            height: 100%;
            margin-left: 80px;
            padding: 0;
            background-color: var(--background-color);
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
        }

        .exp-sideM {
            position: fixed !important;
            width: 320px;
            height: 100%;
            background-color: white;
            z-index: 1;
            overflow: auto;
            padding: 24px;
            border-right: 1px solid var(--medium-gray);
            box-shadow: var(--box-shadow);
        }

        .top-menu {
            height: 80px;
            padding: 20px 32px;
            background-color: white;
            margin-top: 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--medium-gray);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .chat-box {
            height: 100%;
            overflow: auto;
            padding: 32px 0;
            border-radius: var(--border-radius);
            border: none;
            background: transparent;
        }

        * {
            box-sizing: border-box; /* Include padding and border in total width/height */
        }

        .input-area {
            padding: 20px 32px 20px 32px;
            height: 140px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            border-top: 2px solid var(--medium-gray);
        }

        .input-container {
            position: relative;
            background: var(--light-gray);
            border-radius: 24px;
            border: 1px solid var(--medium-gray);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 12px 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transition: var(--transition);
            min-width: 35vw; 
            max-width: 55vw;
            margin: 0 auto;
        }
        
        .input-container:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 4px 24px rgba(0,0,0,0.12);
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            left: -15px;
            bottom: 70px;
            z-index: 1000;
            background-color: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            border: 1px solid var(--medium-gray);
            padding: 24px;
            width: 420px;
            min-height: 160px;
            box-sizing: border-box;
            overflow: hidden;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .dropdown-content button {
            color: var(--text-color);
            padding: 16px 20px;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--light-gray);
            border: 1px solid var(--medium-gray);
            border-radius: 12px;
            margin: 0;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            width: 100%;
            transition: var(--transition);
            position: relative;
            min-height: 56px;
            box-sizing: border-box;
            overflow: hidden;
        }

        .dropdown-content button span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
            min-width: 0;
            max-width: 300px;
        }



        .dropdown-content button i {
            flex-shrink: 0;
            margin-right: 12px;
        }

        .dropdown:hover .dropdown-content {
            display: flex;
        }

        textarea {
            resize: none;
            width: 100%;
            height: 56px;
            max-height: 120px;
            color: var(--text-color);
            font-size: 20px;
            font-weight: 400;
            font-family: var(--font-family);
            overflow-wrap: break-word;
            flex-grow: 1;
            border: none;
            outline: none;
            padding: 18px 0;
            line-height: 1.2;
            background: transparent;
            display: flex;
            align-items: center;
        }

        .icon-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--primary-color);
        }

        #user_input, #user_input_zh_cn, #user_input_zh_tw {
            border-radius: 0;
            border: 0;
            background: transparent;
            outline: none;
        }

        .com-logo {
            height: 50px;
            background-color: rgba(114, 211, 112, 0);
            margin-top: 10px;
            margin-left: 12px;
        }

        .history-window {
            height: calc(100% - 50px - 100px);
            margin-top: 10px;
            padding: 10px;
            background-color: rgba(248, 248, 248, 0);

            scroll-behavior: auto;
            overflow: auto;
            display: flex;
            align-content: center;
            justify-content: center;

        }

        .func-menu {
            height: 100px;
            margin-top: 10px;
            margin-bottom: 10px;
            background-color: rgba(174, 226, 177, 0);
        }

        .frame {
            display: flex;
            flex-wrap: wrap;
            width: 550px;

            align-items: center;
            align-self: center;
            justify-content: center;
            gap: 22px 160px;
            position: relative;
        }

        .frame .deloitte {
            position: relative;
            width: 250px;
            height: 54.3px;
        }

        .frame .ID-input {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 13px 15px;
            position: relative;
            flex: 0 0 auto;
            border-radius: 36px;
            overflow: hidden;
            border: 1.5px solid;
            border-color: var(--backup);
        }

        .frame .text-wrapper {
            position: relative;
            width: 515px;
            height: 21px;
            margin-top: -1.5px;
            font-family: "Inter-Bold", Helvetica;
            font-weight: 700;
            color: var(--backup);
            font-size: 17px;
            letter-spacing: 0;
            line-height: normal;
        }

        .frame .overlap-group-wrapper {
            position: relative;
            width: 550px;
            height: 48px;
        }

        .frame .overlap-group {
            position: relative;
            height: 48px;
            border-radius: 36px;
        }

        .frame .div {
            position: absolute;
            width: 311px;
            top: 13px;
            left: 15px;
            font-family: "Inter-Bold", Helvetica;
            font-weight: 700;
            color: var(--backup);
            font-size: 17px;
            letter-spacing: 0;
            line-height: normal;
        }

        .frame .rectangle {
            position: absolute;
            width: 550px;
            height: 48px;
            top: 0;
            left: 0;
            border-radius: 36px;
            border: 1.5px solid;
            border-color: var(--backup);
        }

        .frame .login-enter-button {
            position: absolute;
            width: 36px;
            height: 36px;
            top: 7px;
            left: 505px;
        }

        .frame .div-2 {
            display: inline-flex;
            align-items: center;
            gap: 11px;
            position: relative;
            flex: 0 0 auto;
        }

        .frame .line-stroke {
            position: relative;
            width: 255px;
            height: 2px;
        }

        .frame .text-wrapper-2 {
            position: relative;
            width: fit-content;
            margin-top: -1px;
            font-family: "Inter-Bold", Helvetica;
            font-weight: 700;
            color: var(--main-dark);
            font-size: 15px;
            text-align: right;
            letter-spacing: 0;
            line-height: normal;
            white-space: nowrap;
        }

        .frame .SSO-button {
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            position: relative;
            flex: 0 0 auto;
        }

        .frame .ellipse {
            position: relative;
            width: 60px;
            height: 60px;
        }

        .frame .text-wrapper-3 {
            position: relative;
            width: fit-content;
            font-family: "Inter-Bold", Helvetica;
            font-weight: 700;
            color: var(--backup);
            font-size: 12px;
            letter-spacing: 0;
            line-height: normal;
        }

        .frame .key-svgrepo-com {
            position: absolute;
            width: 38px;
            height: 38px;
            top: 11px;
            left: 11px;
        }

        .frame .registration-button {
            all: unset;
            box-sizing: border-box;
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            position: relative;
            flex: 0 0 auto;
        }

        .frame .ellipse-2 {
            position: relative;
            width: 60px;
            height: 60px;
            border-radius: 30px;
            border: 1.8px solid;
            border-color: var(--main-light);
        }

        .frame .vector {
            position: absolute;
            width: 34px;
            height: 35px;
            top: 14px;
            left: 16px;

            .file-button {
                display: inline-flex;
                height: 35px;
                align-items: center;
                gap: 7px;
                padding: 4px 6px;
                position: relative;
                background-color: var(--main-light);
                border-radius: 18px;
                overflow: hidden;
                border: 2px solid;
                border-color: var(--main-dark);
            }

            .file-button .document-svgrepo-com {
                position: relative;
                width: 26px;
                height: 26px;
            }

            .file-button .text-wrapper {
                position: relative;
                width: fit-content;
                margin-top: -2px;
                font-family: "Inter-Bold", Helvetica;
                font-weight: 700;
                color: var(--main-dark);
                font-size: 22px;
                text-align: center;
                letter-spacing: 0;
                line-height: normal;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
            }

            .file-button .demo-folder {
                position: relative;
                width: 179px;
                margin-top: -2px;
                font-family: "Inter-Bold", Helvetica;
                font-weight: 700;
                color: var(--main-dark);
                font-size: 22px;
                letter-spacing: 0;
                line-height: normal;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
            }

        }

        #pdf-container {
            position: fixed;
            right: 0;
            top: 0;
            height: 100%;
            border-left: solid;
            background: #ffffff;
            transform: translateX(100%);
            -webkit-transform: translateX(100%);
            z-index: 200;
        }

        .pdf-slide-in {
            animation: pdf-slide-in 0.5s forwards;
            -webkit-animation: pdf-slide-in 0.5s forwards;
            width: 40%;
            min-width: 250px;
        }

        .pdf-slide-out {
            animation: pdf-slide-out 0.5s forwards;
            -webkit-animation: pdf-slide-out 0.5s forwards;
        }

        /* Adjust main content when PDF is open */
        .main-win.pdf-open {
            width: calc(60% - 80px);
        }

        .main-win.pdf-open .chat-window {
            max-width: none;
            margin-left: auto;
            margin-right: auto;
            width: 100%;
        }

        .main-win.pdf-open .top-menu {
            margin-right: 0;
        }

        .main-win.pdf-open .input-area {
            margin-right: 0;
        }

        @keyframes pdf-slide-in {
            100% {
                transform: translateX(0%);
            }
        }

        @-webkit-keyframes pdf-slide-in {
            100% {
                -webkit-transform: translateX(0%);
            }
        }

        @keyframes pdf-slide-out {
            0% {
                transform: translateX(0%);
                width: 40%;
                min-width: 250px;
            }

            100% {
                width: 0%;
                min-width: 0px;
                transform: translateX(100%);
            }
        }

        @-webkit-keyframes pdf-slide-out {
            0% {
                -webkit-transform: translateX(0%);
            }

            100% {
                -webkit-transform: translateX(100%);
            }
        }

        #chatBox h1, #chatBox h2, #chatBox h3, #chatBox h4, #chatBox h5, #chatBox h6 {
    color: white;
    font-weight: 600;
    margin-top: 16px;
    margin-bottom: 12px;
    line-height: 1.3;
}

#chatBox h1 {
    font-size: 22px;
}

#chatBox h2 {
    font-size: 20px;
}

#chatBox h3 {
    font-size: 18px;
}

#chatBox p {
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5;
}

#chatBox ul, #chatBox ol {
    margin-left: 24px;
    margin-bottom: 16px;
    color: white;
    font-size: 16px;
    font-weight: 400;
}

#chatBox li {
    margin-bottom: 8px;
    line-height: 1.5;
}

#chatBox strong {
    font-weight: 600; 
}

#chatBox code {
    font-family: 'SF Mono', 'Consolas', 'Monaco', monospace;
    font-size: 14px;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 2px 4px;
    border-radius: 4px;
}

#chatBox pre {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 12px;
    margin: 12px 0;
    overflow-x: auto;
}
    </style>

</head>

<body>

    <div id="offcanvasChatHistory" class="offcanvas offcanvas-start" tabindex="-1"
        aria-labelledby="offcanvasChatHistoryLabel">

        <section class="com-logo">

            <img src="{{ url_for('static', filename='images/Deloitte.svg') }}"
                style="width: 200px; display: block; margin-top: 10px; margin-left: 10px;">

        </section>

        <section class="history-window">

            <div class="chat-history-container">
                <div id="chatHistoryBox"></div>
            </div>

        </section>

        <section class="func-menu">

            <div class="icon-container"
                style="display: flex; align-self:center; justify-content: center;  bottom: 0; width: 100%; padding-top: 20px; border: none;">
                <button type="button" class="icon-btn database-icon" data-bs-toggle="modal"
                    data-bs-target="#databaseModal" style="background: var(--light-gray); border-radius: 12px; width: 68px; height: 68px; display: flex; align-items: center; justify-content: center; margin: 0px 30px; transition: var(--transition);">
                    <i style="color:#000000" class="fa-regular fa-file-lines fa-xl"></i>
                </button>
                <button type="button" class="icon-btn settings-icon" data-bs-toggle="modal"
                    data-bs-target="#settingsModal" style="background: var(--light-gray); border-radius: 12px; width: 68px; height: 68px; display: flex; align-items: center; justify-content: center; margin: 0px 30px; border: none; transition: var(--transition);">
                    <i style="color:#000000" class="fa-solid fa-gears fa-xl"></i>
                </button>

            </div>

        </section>
    </div>

    <div class="sideM">
        <button class="toggle-chat-history-btn" type="button" style="border: none; padding: 16px; margin-top: 22px; background: var(--light-gray); border-radius: 12px; transition: var(--transition);" data-bs-toggle="offcanvas"
            data-bs-target="#offcanvasChatHistory" aria-controls="offcanvasChatHistory">
            <i class="fa-solid fa-bars fa-lg" style="color: #000000;"></i>
        </button>
    </div>

    <main class="main-win">
        <aside id="pdf-reference" style="display:inline;">

            <div id="pdf-container">

            </div>

        </aside>
        <section class="top-menu">
            <div style="display: flex; align-items: center; gap: 12px;">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <img src="{{ url_for('static', filename='images/chat.png') }}" style="width: 32px; height: 32px; vertical-align: middle;">
                    <h1 style="margin: 0; font-size: 26px; font-weight: 600; color: var(--primary-color);">SAPITOR</h1>
                </div>
            </div>
            <button id="addChatHistoryButton" type="button"
                style="padding: 8px 32px; background: var(--primary-color); color: white; border: none; border-radius: 24px; justify-content: center; align-items: center; gap: 8px; display: inline-flex; font-weight: 600; font-size: 18px; transition: var(--transition); box-shadow: 0 4px 14px rgba(0,0,0,0.15); min-height: 48px;">
                <span class="lang-en">New Chat</span>
                <span class="lang-zh-cn" style="display: none">新对话</span>
                <span class="lang-zh-tw" style="display: none">新對話</span>
            </button>
        </section>

        <section class="chat-window">
            <div id="chatBox" class="chat-box">
            </div>
        </section>

        <section class="input-area">
            <form id="chatForm" class="chat-form" enctype="multipart/form-data">
                <div class="input-container">
                    <div class="dropdown" onmouseover="showDropdown()" onmouseout="hideDropdown()">
                        <button class="icon-btn" type="button" style="margin-left: 10px"><i
                                class="fa-solid fa-list fa-xl" style="color: #000000;"></i></i></button>
                        <div class="dropdown-content" id="drpCon" style="display: none ">
                            <button type="button" title="Active: Demo Folder 1">
                                <i class="fa-regular fa-file-lines fa-xl" style="color:#000000"></i> 
                                <span class="lang-en">Active: Demo Folder 1 </span>
                                <span class="lang-zh-cn" style="display: none">已激活: Demo Folder 1</span>
                                <span class="lang-zh-tw" style="display: none">已激活: Demo Folder 1</span>
                            </button>
                            <button type="button" title="Model: InternVL3-78B">
                                <i class="fa-solid fa-gears fa-xl" style="color:#000000"></i> 
                                <span class="lang-en">Model: InternVL3-78B</span>
                                <span class="lang-zh-cn" style="display: none">模型: InternVL3-78B</span>
                                <span class="lang-zh-tw" style="display: none">模型: InternVL3-78B</span>
                            </button>
                        </div>
                    </div>
                    <textarea type="text" name="user_input" id="user_input" placeholder="Type your message..." class="lang-en"></textarea>
                    <textarea type="text" name="user_input_zh_cn" id="user_input_zh_cn" placeholder="输入您的消息..." class="lang-zh-cn" style="display: none"></textarea>
                    <textarea type="text" name="user_input_zh_tw" id="user_input_zh_tw" placeholder="輸入您的訊息..." class="lang-zh-tw" style="display: none"></textarea>
                    <div
                        style="margin-top:0px; margin-right: 15px; flex-direction: column; justify-content: center; align-content: center">
                        <button style="height: 50px; align-self: center" type="submit" class="icon-btn send-icon">
                            <i class="fa-solid fa-circle-up fa-2xl" style="color: #000000;"></i>
                        </button>
                    </div>
                </div>
            </form>
        </section>

    </main>

    <div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true"
        style=" justify-content: center; align-content: center">
        <div class="modal-dialog " style="justify-content: center; align-content: center">
            <div class="modal-content" style="border-radius: 1.5rem ; ">
                <div class="modal-header" style="border:none; padding: 1rem 1.5rem">
                    <h2 style="color: var(--primary-color); font-weight: 600; font-size: 20px; margin-left:5px" class="lang-en">Settings</h2>
                    <h2 style="color: var(--primary-color); font-weight: 600; font-size: 20px; margin-left:5px; display: none" class="lang-zh-cn">设置</h2>
                    <h2 style="color: var(--primary-color); font-weight: 600; font-size: 20px; margin-left:5px; display: none" class="lang-zh-tw">設置</h2>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="padding: 24px; background: var(--light-gray); border-radius: var(--border-radius); margin: 0 24px;">
                    <select name="mode" id="mode" onchange="updateModelOptions()"
                        style="visibility: hidden;color:#DDEFE8; border-radius: 10px">
                        <option value="online">Online</option>
                        <option value="offline" selected>Offline</option>
                    </select>
                    
                    <!-- Language Selection -->
                    <div class="setting-row" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;">
                        <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px;" class="lang-en">Language:</label>
                        <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px; display: none" class="lang-zh-cn">语言:</label>
                        <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px; display: none" class="lang-zh-tw">語言:</label>
                        <select name="language" id="language" onchange="changeLanguage()"
                            style="flex: 1; color: var(--text-color); padding: 12px 16px; border: 1px solid var(--medium-gray); border-radius: 8px; background-color: white; font-size: 14px; max-width: 250px;">
                            <option value="en" selected>English</option>
                            <option value="zh-cn">简体中文</option>
                            <option value="zh-tw">繁體中文</option>
                        </select>
                    </div>

                    <!-- Language Selection -->
                    <!-- Theme Selection -->
                    <div class="setting-row" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;">
                        <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px;" class="lang-en">Theme:</label>
                        <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px; display: none" class="lang-zh-cn">主题:</label>
                        <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px; display: none" class="lang-zh-tw">主題:</label>
                        <select name="theme" id="theme" onchange="changeTheme()"
                            style="flex: 1; color: var(--text-color); padding: 12px 16px; border: 1px solid var(--medium-gray); border-radius: 8px; background-color: white; font-size: 14px; max-width: 250px;">
                            <option value="black" selected class="lang-en">Black</option>
                            <option value="black" class="lang-zh-cn" style="display: none">黑色</option>
                            <option value="black" class="lang-zh-tw" style="display: none">黑色</option>
                            
                            <option value="teal" class="lang-en">Teal Blue</option>
                            <option value="teal" class="lang-zh-cn" style="display: none">青蓝色</option>
                            <option value="teal" class="lang-zh-tw" style="display: none">青藍色</option>
                        </select>
                    </div>
                    
                    <div class="setting-row" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;">
                        <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px;" class="lang-en">Model:</label>
                        <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px; display: none" class="lang-zh-cn">模型:</label>
                        <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px; display: none" class="lang-zh-tw">模型:</label>
                        <select name="model" id="model"
                            style="flex: 1; color: var(--text-color); padding: 12px 16px; border: 1px solid var(--medium-gray); border-radius: 8px; background-color: white; font-size: 14px; max-width: 250px;">
                            <option value="gpt-3.5">GPT-3.5</option>
                            <option value="mixtral" style="display:none;" selected>InternVL3-78B</option>
                        </select>
                    </div>
                    
                    <div class="setting-row" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;">
                        <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px;" class="lang-en">RAG:</label>
                        <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px; display: none" class="lang-zh-cn">检索增强:</label>
                        <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px; display: none" class="lang-zh-tw">檢索增強:</label>
                        <select name="rag" id="rag"
                            style="flex: 1; color: var(--text-color); padding: 12px 16px; border: 1px solid var(--medium-gray); border-radius: 8px; background-color: white; font-size: 14px; max-width: 250px;">
                            <option value="off" class="lang-en">Off</option>
                            <option value="off" class="lang-zh-cn" style="display: none">关闭</option>
                            <option value="off" class="lang-zh-tw" style="display: none">關閉</option>
                            
                            <option value="on" selected class="lang-en">On</option>
                            <option value="on" class="lang-zh-cn" style="display: none">开启</option>
                            <option value="on" class="lang-zh-tw" style="display: none">開啟</option>
                        </select>
                    </div>

                    <div class="setting-row" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;">
                        <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px;" for="max_history_no" class="lang-en">Max History Items:</label>
                        <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px; display: none" for="max_history_no" class="lang-zh-cn">最大历史项目:</label>
                        <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px; display: none" for="max_history_no" class="lang-zh-tw">最大歷史項目:</label>
                        <select name="max_history_no" id="max_history_no"
                            style="flex: 1; color: var(--text-color); padding: 12px 16px; border: 1px solid var(--medium-gray); border-radius: 8px; background-color: white; font-size: 14px; max-width: 250px;">
                            <option value="3">3</option>
                            <option value="5">5</option>
                            <option value="7">7</option>
                            <option value="9">9</option>
                        </select>
                    </div>
                    
                    <div class="setting-row" style="margin-bottom: 16px;">
                        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;">
                            <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px;" for="multifile_strategy" class="lang-en">Multi-file Strategy:</label>
                            <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px; display: none" for="multifile_strategy" class="lang-zh-cn">多文件策略:</label>
                            <label style="color: var(--text-color); width: 140px; font-weight: 500; font-size: 15px; display: none" for="multifile_strategy" class="lang-zh-tw">多文件策略:</label>
                            <select name="multifile_strategy" id="multifile_strategy"
                                style="flex: 1; color: var(--text-color); padding: 12px 16px; border: 1px solid var(--medium-gray); border-radius: 8px; background-color: white; font-size: 14px; max-width: 250px;">
                                <option value="priority" selected class="lang-en">Smart Priority</option>
                                <option value="priority" class="lang-zh-cn" style="display: none">智能优先级</option>
                                <option value="priority" class="lang-zh-tw" style="display: none">智慧優先級</option>
                                
                                <option value="separated" class="lang-en">File Separation</option>
                                <option value="separated" class="lang-zh-cn" style="display: none">文件分离</option>
                                <option value="separated" class="lang-zh-tw" style="display: none">文件分離</option>
                                
                                <option value="original" class="lang-en">Original Strategy</option>
                                <option value="original" class="lang-zh-cn" style="display: none">原始策略</option>
                                <option value="original" class="lang-zh-tw" style="display: none">原始策略</option>
                            </select>
                        </div>
                        <div style="margin-left: 0px; padding: 16px; background-color: white; border-radius: 8px; border: 1px solid var(--medium-gray); margin-top: 8px;">
                            <small style="color: var(--text-color); font-size: 12px; display: block; margin-bottom: 6px;" class="lang-en">
                                <strong>Smart Priority:</strong> Automatically identifies and prioritizes the most relevant files <br>
                            </small>
                            <small style="color: var(--text-color); font-size: 12px; display: block; margin-bottom: 6px;" class="lang-en">
                                <strong>File Separation:</strong> Labels content with its source file <br>
                            </small>
                            <small style="color: var(--text-color); font-size: 12px; display: block;" class="lang-en">
                                <strong>Original Strategy:</strong> Mixes content from all selected files
                            </small>
                            
                            <small style="color: var(--text-color); font-size: 12px; display: none; margin-bottom: 6px;" class="lang-zh-cn">
                                <strong>智能优先级:</strong> 自动识别最相关文件并优先使用 <br>
                            </small>
                            <small style="color: var(--text-color); font-size: 12px; display: none; margin-bottom: 6px;" class="lang-zh-cn">
                                <strong>文件分离:</strong> 标注每段内容的来源文件 <br>
                            </small>
                            <small style="color: var(--text-color); font-size: 12px; display: none;" class="lang-zh-cn">
                                <strong>原始策略:</strong> 混合使用所有选中文件内容
                            </small>
                            
                            <small style="color: var(--text-color); font-size: 12px; display: none; margin-bottom: 6px;" class="lang-zh-tw">
                                <strong>智慧優先級:</strong> 自動識別最相關文件並優先使用
                            </small>
                            <small style="color: var(--text-color); font-size: 12px; display: none; margin-bottom: 6px;" class="lang-zh-tw">
                                <strong>文件分離:</strong> 標註每段內容的來源文件 <br>
                            </small>
                            <small style="color: var(--text-color); font-size: 12px; display: none;" class="lang-zh-tw">
                                <strong>原始策略:</strong> 混合使用所有選中文件內容
                            </small>
                        </div>
                    </div>
                    
                    <div class="checkbox-container" style="visibility: hidden">
                        <input type="checkbox" id="include_history" name="include_history" checked>
                        <label for="include_history">Include Chat History</label>
                    </div>
                </div>
                <div class="modal-footer" style="border:none">

                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="databaseModal" tabindex="-1" aria-labelledby="databaseModalLabel" aria-hidden="true"
        style=" justify-content: center; align-content: center">
        <div class="modal-dialog" style="max-width: 1400px ; justify-content: center; align-content: center">
            <div class="modal-content" style="align-items: center; flex-direction: column; border-radius: 1.5rem;">
                <div class="modal-header dataset-modal-header"
                    style="width: 100%; display: flex; flex-direction: column; align-items: flex-start; border: none; padding: 20px;">

                    <h2 style="color: var(--primary-color); font-weight: 600; font-size: 20px; margin-bottom: 16px;" class="lang-en">Document Management</h2>
                    <h2 style="color: var(--primary-color); font-weight: 600; font-size: 20px; margin-bottom: 16px; display: none" class="lang-zh-cn">文档管理</h2>
                    <h2 style="color: var(--primary-color); font-weight: 600; font-size: 20px; margin-bottom: 16px; display: none" class="lang-zh-tw">文檔管理</h2>

                    <div class="controls"
                        style="width: 100%; display: flex; flex-wrap: wrap; align-items: center; gap: 12px;">
                        <button type="button" class="btn" id="backToDatabaseList" style="padding: 8px; background: transparent; border: none;">
                            <i class="fa-solid fa-arrow-left" style="color: var(--primary-color); font-size: 18px;"></i>
                        </button>

                        <input type="text" class="form-control me-2 lang-en"
                            style="flex-grow: 1; background: white; border: 1px solid var(--medium-gray); border-radius: 8px; padding: 8px 16px;"
                            placeholder="Search documents">
                        <input type="text" class="form-control me-2 lang-zh-cn" style="flex-grow: 1; background: white; border: 1px solid var(--medium-gray); border-radius: 8px; padding: 8px 16px; display: none"
                            placeholder="搜索文档">
                        <input type="text" class="form-control me-2 lang-zh-tw" style="flex-grow: 1; background: white; border: 1px solid var(--medium-gray); border-radius: 8px; padding: 8px 16px; display: none"
                            placeholder="搜尋文檔">

                        <button class="btn" id="createFolderButton"
                            style="background: var(--primary-color); color: white; border-radius: 8px; padding: 8px 16px; font-size: 14px; font-weight: 500; display: flex; align-items: center; gap: 8px;">
                            <span class="fas fa-folder-plus"></span>
                            <span class="lang-en">New Dataset</span>
                            <span class="lang-zh-cn" style="display: none">新数据集</span>
                            <span class="lang-zh-tw" style="display: none">新數據集</span>
                        </button>

                        <button class="btn" id="deleteButton"
                            style="background: #f5f5f5; margin-left: 5px; color: #666; border-radius: 8px; padding: 8px 12px; border: 1px solid var(--medium-gray);">
                            <span class="fas fa-trash"></span>
                        </button>

                        <button id="uploadLoading"
                            style="visibility: hidden; display: flex; align-items: center; gap: 8px; margin-left: 5px; border: none; background-color: transparent;">
                            <div class="docu-loader"></div>
                            <div style="font-family: var(--font-family); font-size: 14px; color: var(--text-color);">
                                Uploading...</div>
                        </button>
                    </div>
                </div>

                <div class="modal-body dataset-modal-body"
                    style="position: relative; height: 600px; width: 96%; overflow-y: auto; background: var(--light-gray); border: none; border-radius: var(--border-radius); margin-bottom: 16px; padding: 0;">
                    <div class="list-header"
                        style="background: white; padding: 16px; border-bottom: 1px solid var(--medium-gray); display: flex; justify-content: space-between; align-items: center; position: sticky; top: 0; z-index: 10;">
                        <span class="flex-grow-1 dataset-name lang-en"
                            style="color: var(--text-color); font-family: var(--font-family); font-weight: 600; font-size: 14px;">Name</span>
                        <span class="flex-grow-1 dataset-name lang-zh-cn" style="color: var(--text-color); font-family: var(--font-family); font-weight: 600; font-size: 14px; display: none">名称</span>
                        <span class="flex-grow-1 dataset-name lang-zh-tw" style="color: var(--text-color); font-family: var(--font-family); font-weight: 600; font-size: 14px; display: none">名稱</span>
                        
                        <span class="active-text lang-en"
                            style="margin-right: 5px; color: var(--text-color); font-family: var(--font-family); font-weight: 600; font-size: 14px;">Active</span>
                        <span class="active-text lang-zh-cn"
                            style="margin-right: 5px; color: var(--text-color); font-family: var(--font-family); font-weight: 600; font-size: 14px; display: none">激活</span>
                        <span class="active-text lang-zh-tw"
                            style="margin-right: 5px; color: var(--text-color); font-family: var(--font-family); font-weight: 600; font-size: 14px; display: none">激活</span>
                    </div>

                    <div id="databaseList" class="list-group" style="padding: 8px;"></div>
                    <div class="list-group" style="display: none; padding: 8px;" id="fileList"></div>
                    <input type="file" id="file-upload" style="display: none;" multiple>
                    <button id="uploadFileButton" class="btn btn-primary"
                        style="position: absolute; right: 16px; bottom: 16px; background-color: var(--primary-color); color: white; width: 48px; height: 48px; border-radius: 50%; box-shadow: 0 2px 10px rgba(0,0,0,0.2); display: flex; align-items: center; justify-content: center;">
                        <span class="fas fa-plus"></span>
                    </button>
                </div>

                <div class="modal-footer" style="border-top: 1px solid var(--medium-gray); padding: 16px;">
                    <div style="font-family: var(--font-family); color: #666; font-size: 13px;" class="lang-en">
                        Note: only files in the same folders can be activated.
                    </div>
                    <div style="font-family: var(--font-family); color: #666; font-size: 13px; display: none" class="lang-zh-cn">
                        注意：只能激活同一文件夹中的文件。
                    </div>
                    <div style="font-family: var(--font-family); color: #666; font-size: 13px; display: none" class="lang-zh-tw">
                        注意：只能激活同一文件夾中的文件。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="createFolderModal" tabindex="-1" aria-labelledby="createFolderModalLabel"
        aria-hidden="true" style="justify-content: center; align-content: center">
        <div class="modal-dialog">
            <div class="modal-content" style="border-radius: var(--border-radius); background-color: white; border: none; box-shadow: var(--box-shadow);">

                <div class="modal-header" style="border-bottom: 1px solid var(--medium-gray); padding: 16px 24px;">
                    <h2 style="color: var(--primary-color); font-weight: 600; font-size: 18px;" class="lang-en">Create New Dataset</h2>
                    <h2 style="color: var(--primary-color); font-weight: 600; font-size: 18px; display: none" class="lang-zh-cn">创建新数据集</h2>
                    <h2 style="color: var(--primary-color); font-weight: 600; font-size: 18px; display: none" class="lang-zh-tw">創建新數據集</h2>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body" style="padding: 24px;">
                    <label for="newFolderName" class="form-label lang-en" style="font-weight: 500; margin-bottom: 8px; color: var(--text-color);">Dataset Name</label>
                    <label for="newFolderName-zh-cn" class="form-label lang-zh-cn" style="font-weight: 500; margin-bottom: 8px; color: var(--text-color); display: none">数据集名称</label>
                    <label for="newFolderName-zh-tw" class="form-label lang-zh-tw" style="font-weight: 500; margin-bottom: 8px; color: var(--text-color); display: none">數據集名稱</label>
                    
                    <input type="text" class="form-control lang-en" id="newFolderName" placeholder="Enter dataset name" 
                        style="padding: 10px 16px; border: 1px solid var(--medium-gray); border-radius: 6px;">
                    <input type="text" class="form-control lang-zh-cn" id="newFolderName-zh-cn" placeholder="输入数据集名称" 
                        style="padding: 10px 16px; border: 1px solid var(--medium-gray); border-radius: 6px; display: none">
                    <input type="text" class="form-control lang-zh-tw" id="newFolderName-zh-tw" placeholder="輸入數據集名稱" 
                        style="padding: 10px 16px; border: 1px solid var(--medium-gray); border-radius: 6px; display: none">
                </div>

                <div class="modal-footer" style="border-top: 1px solid var(--medium-gray); padding: 16px 24px;">
                    <button type="button" class="btn" data-bs-dismiss="modal" 
                        style="background: transparent; color: var(--text-color); border: 1px solid var(--medium-gray); border-radius: 6px; padding: 8px 16px; margin-right: 12px;">
                        <span class="lang-en">Cancel</span>
                        <span class="lang-zh-cn" style="display: none">取消</span>
                        <span class="lang-zh-tw" style="display: none">取消</span>
                    </button>
                    <button type="button" class="btn btn-primary" id="confirmCreateFolder" 
                        style="background: var(--primary-color); color: white; border: none; border-radius: 6px; padding: 8px 16px; font-weight: 500;">
                        <span class="lang-en">Create</span>
                        <span class="lang-zh-cn" style="display: none">创建</span>
                        <span class="lang-zh-tw" style="display: none">創建</span>
                    </button>
                </div>

            </div>
        </div>
    </div>

    <div class="modal fade" id="warningWin" tabindex="-1" aria-labelledby="warningModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="border-radius: var(--border-radius); border: none; box-shadow: var(--box-shadow);">
                <div class="modal-header" style="border-bottom: 1px solid var(--medium-gray); padding: 16px 24px;">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-circle" style="color: var(--primary-color); font-size: 24px; margin-right: 12px;"></i>
                        <h5 style="color: var(--primary-color); font-size: 18px; font-weight: 600; margin: 0;" class="lang-en">Warning</h5>
                        <h5 style="color: var(--primary-color); font-size: 18px; font-weight: 600; margin: 0; display: none" class="lang-zh-cn">警告</h5>
                        <h5 style="color: var(--primary-color); font-size: 18px; font-weight: 600; margin: 0; display: none" class="lang-zh-tw">警告</h5>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body" style="padding: 24px;">
                    <p style="color: var(--text-color); font-size: 16px; line-height: 1.5; text-align: center;" class="lang-en">
                        No document is currently selected. RAG is off now.
                    </p>
                    <p style="color: var(--text-color); font-size: 16px; line-height: 1.5; text-align: center; display: none" class="lang-zh-cn">
                        当前未选择任何文档。检索增强功能已关闭。
                    </p>
                    <p style="color: var(--text-color); font-size: 16px; line-height: 1.5; text-align: center; display: none" class="lang-zh-tw">
                        當前未選擇任何文檔。檢索增強功能已關閉。
                    </p>
                </div>

                <div class="modal-footer" style="border-top: 1px solid var(--medium-gray); padding: 16px 24px; justify-content: center;">
                    <button type="button" class="btn" data-bs-dismiss="modal" 
                        style="background: var(--primary-color); color: white; border: none; border-radius: 6px; padding: 8px 24px; font-weight: 500;">
                        <span class="lang-en">OK</span>
                        <span class="lang-zh-cn" style="display: none">确定</span>
                        <span class="lang-zh-tw" style="display: none">確定</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div style="visibility: hidden; display: none; z-index: -1; width: 1px; height: 1px;">
        <i class="fa-solid fa-robot fa-xl" style="color: #000000;" alt="Image Description"></i>
        <i class="fa-regular fa-user fa-xl" style="color: #000000;" alt="Image Description"></i>
    </div>

    <script src="https://unpkg.com/pdfobject"></script>
    <script>
        // Store the current language
        let currentLanguage = 'en'; // Default language is English
        
        // Function to change the language of the UI
        function changeLanguage() {
            // Get the selected language
            const language = document.getElementById('language').value;
            currentLanguage = language;
            
            // Hide all language elements
            document.querySelectorAll('.lang-en, .lang-zh-cn, .lang-zh-tw').forEach(el => {
                el.style.display = 'none';
            });
            
            // Show only the elements for the selected language
            document.querySelectorAll('.lang-' + language).forEach(el => {
                el.style.display = '';
            });
            
            // Store the language preference in localStorage
            localStorage.setItem('preferredLanguage', language);
            
            // Handle special cases for textarea
            if (language === 'en') {
                document.getElementById('user_input').style.display = '';
                document.getElementById('user_input_zh_cn').style.display = 'none';
                document.getElementById('user_input_zh_tw').style.display = 'none';
            } else if (language === 'zh-cn') {
                document.getElementById('user_input').style.display = 'none';
                document.getElementById('user_input_zh_cn').style.display = '';
                document.getElementById('user_input_zh_tw').style.display = 'none';
            } else if (language === 'zh-tw') {
                document.getElementById('user_input').style.display = 'none';
                document.getElementById('user_input_zh_cn').style.display = 'none';
                document.getElementById('user_input_zh_tw').style.display = '';
            }
            
            // Update dropdown options for select elements
            updateSelectOptions();
            
            // Update tooltip texts based on language
            updateTooltipTexts();
            
            // Update dropdown content with current theme and language
            updateDropdownContent();
            
            console.log('Language changed to:', language);
        }
        
        // Function to update tooltip texts based on current language
        function updateTooltipTexts() {
            const dropdownButtons = document.querySelectorAll('.dropdown-content button');
            if (dropdownButtons.length >= 2) {
                if (currentLanguage === 'en') {
                    dropdownButtons[0].title = "Active: Demo Folder 1";
                    dropdownButtons[1].title = "Model: InternVL3-78B";
                } else if (currentLanguage === 'zh-cn') {
                    dropdownButtons[0].title = "已激活: Demo Folder 1";
                    dropdownButtons[1].title = "模型: InternVL3-78B";
                } else if (currentLanguage === 'zh-tw') {
                    dropdownButtons[0].title = "已激活: Demo Folder 1";
                    dropdownButtons[1].title = "模型: InternVL3-78B";
                }
            }
        }
        
        // Function to update select options based on language
        function updateSelectOptions() {
            // Handle theme select
            const themeSelect = document.getElementById('theme');
            if (themeSelect) {
                const currentTheme = themeSelect.value;
                // Clear existing options
                themeSelect.innerHTML = '';
                
                // Add new options based on language
                if (currentLanguage === 'en') {
                    themeSelect.add(new Option('Black', 'black', currentTheme === 'black'));
                    themeSelect.add(new Option('Teal Blue', 'teal', currentTheme === 'teal'));
                } else if (currentLanguage === 'zh-cn') {
                    themeSelect.add(new Option('黑色', 'black', currentTheme === 'black'));
                    themeSelect.add(new Option('青蓝色', 'teal', currentTheme === 'teal'));
                } else if (currentLanguage === 'zh-tw') {
                    themeSelect.add(new Option('黑色', 'black', currentTheme === 'black'));
                    themeSelect.add(new Option('青藍色', 'teal', currentTheme === 'teal'));
                }
            }
            
            // Handle multifile_strategy select
            const multifileSelect = document.getElementById('multifile_strategy');
            if (multifileSelect) {
                // Clear existing options
                multifileSelect.innerHTML = '';
                
                // Add new options based on language
                if (currentLanguage === 'en') {
                    multifileSelect.add(new Option('Smart Priority', 'priority', true));
                    multifileSelect.add(new Option('File Separation', 'separated'));
                    multifileSelect.add(new Option('Original Strategy', 'original'));
                } else if (currentLanguage === 'zh-cn') {
                    multifileSelect.add(new Option('智能优先级', 'priority', true));
                    multifileSelect.add(new Option('文件分离', 'separated'));
                    multifileSelect.add(new Option('原始策略', 'original'));
                } else if (currentLanguage === 'zh-tw') {
                    multifileSelect.add(new Option('智慧優先級', 'priority', true));
                    multifileSelect.add(new Option('文件分離', 'separated'));
                    multifileSelect.add(new Option('原始策略', 'original'));
                }
            }
            
            // Handle rag select
            const ragSelect = document.getElementById('rag');
            if (ragSelect) {
                // Clear existing options
                ragSelect.innerHTML = '';
                
                // Add new options based on language
                if (currentLanguage === 'en') {
                    ragSelect.add(new Option('Off', 'off'));
                    ragSelect.add(new Option('On', 'on', true, true)); // Set as selected and default
                } else if (currentLanguage === 'zh-cn') {
                    ragSelect.add(new Option('关闭', 'off'));
                    ragSelect.add(new Option('开启', 'on', true, true)); // Set as selected and default
                } else if (currentLanguage === 'zh-tw') {
                    ragSelect.add(new Option('關閉', 'off'));
                    ragSelect.add(new Option('開啟', 'on', true, true)); // Set as selected and default
                }
                
                // Force the value to be 'on' after adding options
                setTimeout(() => {
                    ragSelect.value = 'on';
                    console.log("🎯 RAG forced to 'on' after language change, current value:", ragSelect.value);
                }, 10);
            }
        }
        
        // Function to change the theme
        function changeTheme() {
            const theme = document.getElementById('theme').value;
            const body = document.body;
            
            // Remove existing theme classes
            body.classList.remove('theme-black', 'theme-teal');
            
            // Apply new theme class
            if (theme === 'teal') {
                body.classList.add('theme-teal');
            } else {
                body.classList.add('theme-black');
            }
            
            // Update CSS custom properties immediately
            const primaryColor = theme === 'teal' ? '#004F59' : '#000000';
            document.documentElement.style.setProperty('--primary-color', primaryColor);
            document.documentElement.style.setProperty('--accent-color', primaryColor);
            
            // Update all SVG colors and text colors
            updateThemeColors(theme);
            
            // Store the theme preference in localStorage
            localStorage.setItem('preferredTheme', theme);
            
            console.log('Theme changed to:', theme);
        }
        
        // Function to update SVG colors and other theme-specific elements
        function updateThemeColors(theme) {
            const primaryColor = theme === 'teal' ? '#004F59' : '#000000';
            
            // Update all SVG elements that should match the theme (including both black and teal colors)
            const svgElements = document.querySelectorAll('svg path[fill="#000000"], svg path[fill="#004F59"], svg path[fill="#F90505"]');
            svgElements.forEach(path => {
                const currentFill = path.getAttribute('fill');
                // Keep red color for specific elements (like citation icons)
                if (currentFill === '#F90505') return;
                // Update black or teal elements to the new primary color
                if (currentFill === '#000000' || currentFill === '#004F59') {
                    path.setAttribute('fill', primaryColor);
                }
            });
            
            // Update icon colors (handle both black and teal colors)
            const iconElements = document.querySelectorAll('i[style*="color:"], i[class*="fa-"]');
            iconElements.forEach(icon => {
                const style = icon.getAttribute('style');
                if (style) {
                    // Replace both black and teal colors with the new primary color
                    let newStyle = style.replace(/color:\s*#000000/g, `color: ${primaryColor}`);
                    newStyle = newStyle.replace(/color:\s*#004F59/g, `color: ${primaryColor}`);
                    newStyle = newStyle.replace(/color:#000000/g, `color: ${primaryColor}`);
                    newStyle = newStyle.replace(/color:#004F59/g, `color: ${primaryColor}`);
                    icon.setAttribute('style', newStyle);
                }
            });
            
            // Also update any elements that use CSS custom properties
            document.documentElement.style.setProperty('--primary-color', primaryColor);
            document.documentElement.style.setProperty('--accent-color', primaryColor);
            
            // Update dropdown content spans
            updateDropdownContent();
            
            // Force refresh any dynamically created elements
            refreshAllThemeElements(primaryColor);
        }
        
        // Function to refresh all theme elements (for comprehensive updates)
        function refreshAllThemeElements(primaryColor) {
            // Update all elements with specific classes that should match theme
            const themeElements = document.querySelectorAll('.send-icon, .page-button, .toggle-chat-history-btn, .icon-btn');
            themeElements.forEach(element => {
                const style = element.getAttribute('style');
                if (style) {
                    let newStyle = style.replace(/color:\s*#[0-9A-Fa-f]{6}/g, `color: ${primaryColor}`);
                    newStyle = newStyle.replace(/background:\s*#[0-9A-Fa-f]{6}/g, `background: ${primaryColor}`);
                    element.setAttribute('style', newStyle);
                }
            });
            
            // Update any remaining SVG paths that might have been missed
            setTimeout(() => {
                const allSvgPaths = document.querySelectorAll('svg path');
                allSvgPaths.forEach(path => {
                    const fill = path.getAttribute('fill');
                    if (fill === '#000000' || fill === '#004F59') {
                        path.setAttribute('fill', primaryColor);
                    }
                });
            }, 50);
        }
        
        // Function to update dropdown content based on current theme and language
        function updateDropdownContent() {
            const theme = document.getElementById('theme').value;
            const language = currentLanguage;
            
            const dropdownButtons = document.querySelectorAll('.dropdown-content button');
            if (dropdownButtons.length >= 2) {
                // Update model name based on theme
                const modelName = theme === 'teal' ? 'InternVL3-78B (Teal)' : 'InternVL3-78B';
                
                if (language === 'en') {
                    dropdownButtons[1].innerHTML = `
                        <i class="fa-solid fa-gears fa-xl" style="color: var(--primary-color);"></i> 
                        <span>Model: ${modelName}</span>
                    `;
                    dropdownButtons[1].title = `Model: ${modelName}`;
                } else if (language === 'zh-cn') {
                    dropdownButtons[1].innerHTML = `
                        <i class="fa-solid fa-gears fa-xl" style="color: var(--primary-color);"></i> 
                        <span>模型: ${modelName}</span>
                    `;
                    dropdownButtons[1].title = `模型: ${modelName}`;
                } else if (language === 'zh-tw') {
                    dropdownButtons[1].innerHTML = `
                        <i class="fa-solid fa-gears fa-xl" style="color: var(--primary-color);"></i> 
                        <span>模型: ${modelName}</span>
                    `;
                    dropdownButtons[1].title = `模型: ${modelName}`;
                }
            }
        }

        // Function to load saved language preference
        function loadLanguagePreference() {
            const savedLanguage = localStorage.getItem('preferredLanguage');
            if (savedLanguage) {
                document.getElementById('language').value = savedLanguage;
                changeLanguage();
            }
        }
        
        // Function to load saved theme preference
        function loadThemePreference() {
            const savedTheme = localStorage.getItem('preferredTheme');
            if (savedTheme) {
                document.getElementById('theme').value = savedTheme;
                changeTheme();
            }
        }
        
        // Function to ensure RAG is always "on" and save preference
        function enforceRAGDefault() {
            // Always set RAG to "on" regardless of any saved preference
            localStorage.setItem('preferredRAG', 'on');
            
            const ragSelect = document.getElementById('rag');
            if (ragSelect) {
                ragSelect.value = 'on';
                console.log("✅ RAG enforced to 'on' and saved to localStorage");
                return true;
            }
            return false;
        }
        
        // Initialize language and theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadLanguagePreference();
            loadThemePreference();
            
            // Enforce RAG default after language/theme initialization
            setTimeout(() => {
                enforceRAGDefault();
            }, 100);
            
            // Add event listener for settings modal to ensure language and theme are applied when modal opens
            $('#settingsModal').on('show.bs.modal', function() {
                // Re-apply language and theme settings when modal opens
                changeLanguage();
                updateSelectOptions();
                
                // Ensure RAG is set to "on" when modal opens
                setTimeout(() => {
                    enforceRAGDefault();
                }, 10);
            });
            
            // Initialize tooltip texts
            updateTooltipTexts();
        });

        let dropdownTimeout;

        function showDropdown() {
            clearTimeout(dropdownTimeout);
            document.querySelector('.dropdown-content').style.display = 'block';
        }

        function hideDropdown() {
            dropdownTimeout = setTimeout(() => {
                document.querySelector('.dropdown-content').style.display = 'none';
            }, 500); // Increased timeout to 1 second for better usability
        }

        function showPDFChunkReference(pages_data, file_name) {
            pages_data = JSON.parse(pages_data.replaceAll("~>~<~", '"'));
            pdfContainer = document.getElementById('pdf-container');
            var isOpen = pdfContainer.classList.contains('pdf-slide-in');
            console.log("show pdf");
            let pdf_path;
            $.ajax({
                url: '/draw_file',
                type: 'post',
                data: JSON.stringify({ pages_data: pages_data, filename: file_name }),
                processData: false,
                contentType: 'application/json',
                success: function (response) {
                    console.log("Response received:", response.response);
                    pdf_path = response.processed_file_path
                    console.log("pdfpath:", pdf_path)

                    PDFObject.embed(pdf_path, "#my-pdf", {
                        pdfOpenParams: { page: 2 },
                        callback: function(pdfObj) {
                            if (pdfObj) {
                                console.log("PDF embedded successfully");

                            } else {
                                console.log("PDF embedding failed");
                            }
                        }
                    });

                    setTimeout(function() {
                        var iframe = document.querySelector("#my-pdf iframe");
                        if (iframe) {
                            iframe.src = pdf_path + "#page=2";
                        }
                    }, 1000);  

                    document.getElementById('my-pdf').addEventListener('load', function() {
                        var iframe = this.querySelector('iframe');
                        if (iframe) {
                            iframe.contentWindow.PDFViewerApplication.page = 2;
                        }
                    });
                },
                error: function (error) {
                    console.error("Error:", error);
                }
            });
            pdfContainer.innerHTML = `
                <div class="headline-container">
                    <div class="filename">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right:15px">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4C7.34315 4 6 5.34315 6 7V17C6 18.6569 7.34315 20 9 20H15C16.6569 20 18 18.6569 18 17V10H15C13.3431 10 12 8.65685 12 7V4H9ZM14 5.41421L16.5858 8H15C14.4477 8 14 7.55228 14 7V5.41421ZM4 7C4 4.23858 6.23858 2 9 2H13C13.2652 2 13.5196 2.10536 13.7071 2.29289L19.7071 8.29289C19.8946 8.48043 20 8.73478 20 9V17C20 19.7614 17.7614 22 15 22H9C6.23858 22 4 19.7614 4 17V7ZM15.2071 12.2929C15.5976 12.6834 15.5976 13.3166 15.2071 13.7071L12.2071 16.7071C11.8166 17.0976 11.1834 17.0976 10.7929 16.7071L9.29289 15.2071C8.90237 14.8166 8.90237 14.1834 9.29289 13.7929C9.68342 13.4024 10.3166 13.4024 10.7071 13.7929L11.5 14.5858L13.7929 12.2929C14.1834 11.9024 14.8166 11.9024 15.2071 12.2929Z" fill="#000000"/>
                        </svg>
                        <p>${file_name}</p>
                    </div>

                    <div onclick="closePDFChunkReference()" class="cancel-button">
                        <svg width="25" height="25" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 25C0 11.1929 11.1929 0 25 0V0C38.8071 0 50 11.1929 50 25V25C50 38.8071 38.8071 50 25 50V50C11.1929 50 0 38.8071 0 25V25Z" fill="#000000"/>
                        <path d="M12.1669 34.2064L21.0198 25.3536L24.6464 28.9802L15.7936 37.8331C14.7921 38.8346 13.1684 38.8346 12.1669 37.8331C11.1654 36.8316 11.1654 35.2079 12.1669 34.2064ZM25 28.6267L21.3733 25L25 21.3733L28.6267 25L25 28.6267ZM21.0198 24.6465L12.1669 15.7936C11.1654 14.7921 11.1654 13.1684 12.1669 12.1669C13.1684 11.1654 14.7921 11.1654 15.7936 12.1669L24.6464 21.0198L21.0198 24.6465ZM28.9802 24.6465L25.3536 21.0198L34.2064 12.1669C35.2079 11.1654 36.8316 11.1654 37.8331 12.1669C38.8346 13.1684 38.8346 14.7921 37.8331 15.7936L28.9802 24.6465ZM25.3536 28.9802L28.9802 25.3536L37.8331 34.2064C38.8346 35.2079 38.8346 36.8316 37.8331 37.8331C36.8316 38.8346 35.2079 38.8346 34.2064 37.8331L25.3536 28.9802Z" fill="white" stroke="#000000" stroke-width="0.5"/>
                        </svg>
                    </div>
                </div>

                <div id="my-pdf"></div> `;

            //     <svg width="40" height="40" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">

            //     <svg width="40" height="40" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">

            //         <svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http://www.w3.org/2000/svg">

            pdfContainer.setAttribute('class', 'pdf-slide-in');
            
            // Add pdf-open class to main-win for layout adjustment
            document.querySelector('.main-win').classList.add('pdf-open');
        }

        function closePDFChunkReference() {

            document.getElementById('pdf-container').setAttribute('class', 'pdf-slide-out');
            
            // Remove pdf-open class from main-win to restore normal layout
            document.querySelector('.main-win').classList.remove('pdf-open');
        }

        const msalConfig = {
            auth: {
                clientId: "802fe47f-0185-4956-8278-91f1c9e2a60b",
                authority: "https://login.microsoftonline.com/27660204-66d0-403c-86f4-2455dc04d732",
                redirectUri: "http://localhost:8008/chat"
            },
            cache: {
                cacheLocation: "localStorage",
                storeAuthStateInCookie: true
            }
        };

        const msalInstance = new msal.PublicClientApplication(msalConfig);

        //         fetch('http://localhost:8000/api/auth/url', {

        //                         fetch('http://localhost:8000/api/auth/token', {

        //                 const response = await fetch('http://localhost:8340/api/admin/user/create', {

        //             fetch(`http://localhost:8000/api/validate_token?token=${token.access_token}`)

        function signOut() {
            fetch('/logout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(response => {
                if (response.ok) {
                    localStorage.removeItem('user');
                    return msalInstance.logoutPopup();
                } else {
                    return Promise.reject('Failed to logout from server');
                }
            }).then(() => {
                window.location.href = '/login_page';
            }).catch(error => {
                console.error(error);
            });
        }

        function loadAllUsersChatHistory() {
            $.ajax({
                url: '/get-all-users-data',
                type: 'GET',
                success: function (data) {
                    const chatHistoryBox = $('#chatHistoryBox');
                    chatHistoryBox.empty();
                    Object.keys(data).forEach(userId => {
                        const firstQuestion = data[userId]['q_msg'];
                        const firstQuestionTime = data[userId]['q_time'];
                        
                        // Skip entries with invalid or empty content
                        if (!firstQuestion || 
                            firstQuestion.trim() === '' || 
                            firstQuestion === 'No question available' ||
                            firstQuestionTime === 'No time available' ||
                            firstQuestion === 'Error loading data') {
                            console.log(`Skipping invalid entry for user ${userId}: "${firstQuestion}"`);
                            return; // Skip this iteration
                        }
                        
                        console.log(data)
                        const entryHtml = `
                    <div class="chat-history-entry" data-userId="${userId}">
                        <div class="chat-history-content">
                            <div class="chat-history-header">
                                <div class="chat-history-time">${firstQuestionTime}</div>
                                <button class="chat-history-delete-btn">
                                    <img src="{{ url_for('static', filename='images/Cross button Frame.svg') }}" alt="Delete" class="delete-icon">
                                </button>
                            </div>
                            <div class="chat-history-divider"></div>
                            <div class="chat-history-body">
                                <div class="chat-history-title-label">Title:&nbsp;</div>
                                <div class="chat-history-title-text">${firstQuestion}</div>
                            </div>
                        </div>
                   </div>
                `;
                        chatHistoryBox.prepend(entryHtml);
                    });
                },
                error: function (xhr, status, error) {
                    console.error("Failed to load all users' chat history:", error);
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function () {
            loadAllUsersChatHistory()
            setTimeout(function () {
                var chatHistoryBox = document.getElementById("chatHistoryBox");
                if (!chatHistoryBox) {
                    console.log("Chat history box not found.");
                    return;
                }

                var entries = chatHistoryBox.getElementsByClassName("chat-history-entry");
                console.log("Entries found: " + entries.length);

                Array.from(entries).forEach(function (entry) {
                    var titleDiv = entry.querySelector("div[style*='width: 246px;']");
                    if (!titleDiv) {
                        console.log("Title div not found in an entry.");
                        return;
                    }
                    console.log(titleDiv)
                    var titleText = titleDiv.textContent;
                    console.log("Checking entry: " + titleText);

                    if (titleText === "No question available") {
                        var deleteButton = entry.querySelector("button[style*='background:#DDEFE8;']");
                        if (deleteButton) {
                            console.log("Deleting entry: " + titleText);
                            deleteButton.click();
                        } else {
                            console.log("Delete button not found.");
                        }
                    }
                });

                var addChatHistoryButton = document.getElementById("addChatHistoryButton");
                if (addChatHistoryButton) {
                    console.log("Adding new chat history entry.");
                    addChatHistoryButton.click();
                } else {
                    console.log("Add chat history button not found.");
                }
            }, 1000);
        });

        $(document).ready(function () {

            const textArea = document.getElementById('user_input');
            const textAreaZhCn = document.getElementById('user_input_zh_cn');
            const textAreaZhTw = document.getElementById('user_input_zh_tw');
            const inputContainer = document.querySelector('.input-container');
            const outerContainer = document.querySelector('.input-container > div > div');
            const submitButton = document.querySelector('.send-icon');

            var currentSelectedDataset;
            var selectedDatasets = []; 
            var selectedFiles = [];

            updateButtonVisibility()
            adjustTextAreaHeight();
            newLoadInitialDatasets();

            updateDeleteButtonStatus();
            loadInstructOptions();

            updateFormValues();
            
            // Ensure RAG is always set to "on" by default when page loads
            // Try immediately
            setTimeout(() => {
                enforceRAGDefault();
            }, 10);
            
            // Try again after language initialization should be complete
            setTimeout(() => {
                enforceRAGDefault();
            }, 200);
            
            // Final enforcement
            setTimeout(() => {
                enforceRAGDefault();
            }, 1000);

            var selectedFilesMap = {};

            document.getElementById('backToDatabaseList').style.display = 'none';
            


            // Removed outdated loadUserChatHistory function - using the updated version below

            //                             <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right:10px">

            function parseMessage(message) {
                console.log('Original message:', message);
                const thinkStart = message.indexOf('<think>');
                const thinkEnd = message.indexOf('</think>');
                let thinking = '';
                let finalAnswer = message.trim();

                if (thinkStart !== -1 && thinkEnd !== -1) {
                    thinking = message.substring(thinkStart + 7, thinkEnd).trim();
                    finalAnswer = message.substring(thinkEnd + 8).trim();
                }

                console.log('Thinking:', thinking);
                console.log('Final Answer:', finalAnswer);
                return { thinking, finalAnswer };
            }

            function appendMessage(message, className, isLoader = false, list_of_chunks = null, contribution_analysis = null) {
                // Clean message from any JSON data that might have leaked through
                var cleanedMessage = message;
                
                // Remove any trailing contribution analysis JSON data that might have been appended
                // Look for multiple patterns that indicate JSON data leakage
                const jsonPatterns = [
                    /~<~,~>~<~score_distribution~>~<~:.*$/s,           // Original pattern
                    /~<~,~>~<~.*$/s,                                   // Any data starting with ~<~,~>~<~
                    /\}\)\'\s*style="background:.*$/s,                 // Button HTML with escaped JSON
                    /\}\"\)\'\s*style="background:.*$/s,               // Variation with quotes
                    /~<~,~>~<~llm_was_used~>~<~:.*$/s,                 // LLM analysis data
                    /~<~,~>~<~bbox~>~<~:.*$/s,                         // Bounding box data
                    /~<~,~>~<~chunk_index~>~<~:.*$/s,                  // Chunk index data
                    /~<~,~>~<~contribution_rank~>~<~:.*$/s,            // Contribution rank data
                    /References \(\d+ most relevant\)\s*~<~.*$/s,     // References section with JSON
                    /• Page \d+ • [^~]*~<~.*$/s                        // Page info with trailing JSON
                ];
                
                for (const pattern of jsonPatterns) {
                    if (pattern.test(cleanedMessage)) {
                        console.log("🧹 Detected and removing leaked JSON/HTML data from response");
                        cleanedMessage = cleanedMessage.replace(pattern, '').trim();
                        // Don't break here - apply all patterns to ensure complete cleaning
                    }
                }
                
                // Additional aggressive cleaning for embedded JSON in citation text
                // Remove any text that looks like: "References (X most relevant)" followed by JSON data
                cleanedMessage = cleanedMessage.replace(/References \(\d+ most relevant\)\s*\n?[^a-zA-Z]*~<~.*$/s, 'References ($1 most relevant)');
                
                // Remove any standalone lines that are purely JSON data (start with ~<~)
                cleanedMessage = cleanedMessage.replace(/^~<~.*$/gm, '');
                
                // Clean up any remaining empty lines or whitespace
                cleanedMessage = cleanedMessage.replace(/\n\s*\n\s*\n/g, '\n\n').trim();
                
                var processedMessage = cleanedMessage.replace(/^\nAnswer: /, '').replace(/^AI:\s*/, '').trim();
                const { thinking, finalAnswer } = parseMessage(processedMessage);

                var isCodeBlock = finalAnswer.startsWith('```') && finalAnswer.endsWith('```');
                var messageContent;
                let filename_list;
                let buttonsHtml = '';
                if (isLoader) {
                    messageContent = '<div class="loader"></div>';
                } else if (isCodeBlock) {
                    var codeContent = finalAnswer.substring(3, finalAnswer.length - 3);
                    messageContent = '<pre><code>' + codeContent + '</code></pre>';
                } else {
                    // Parse markdown to HTML (this handles all formatting including headers, paragraphs, etc.)
                    messageContent = marked.parse(finalAnswer);
                    if (className.includes('bot-message') && (list_of_chunks != null)) {
                        console.log("📋 Processing citations with semantic analysis support", list_of_chunks);
                        
                        // Intelligent citation processing using semantic analysis when available
                        let citationData = generateSmartCitations(list_of_chunks, contribution_analysis);
                        
                        // Generate citation buttons
                        citationData.filesToDisplay.forEach(function (fileInfo, index) {
                            const filename = fileInfo.filename;
                            const displayInfo = fileInfo.displayInfo;
                            
                            // Handle filename matching - try different variations
                            let actualFilename = filename;
                            if (!list_of_chunks[filename]) {
                                // Try to find the correct filename by checking different variations
                                const possibleFilenames = [];
                                
                                // Case 1: filename ends with 'pdf' but not '.pdf' (like 'amberplacepdf')
                                if (filename.endsWith('pdf') && !filename.endsWith('.pdf')) {
                                    // Insert dot before 'pdf': 'amberplacepdf' -> 'amberplace.pdf'
                                    const withDot = filename.slice(0, -3) + '.pdf';
                                    possibleFilenames.push(withDot);
                                }
                                
                                // Case 2: Try adding .pdf extension
                                if (!filename.endsWith('.pdf')) {
                                    possibleFilenames.push(filename + '.pdf');
                                }
                                
                                // Case 3: Try removing .pdf extension if present
                                if (filename.endsWith('.pdf')) {
                                    possibleFilenames.push(filename.replace('.pdf', ''));
                                }
                                
                                // Case 4: Try exact match with different case
                                const availableKeys = Object.keys(list_of_chunks);
                                for (const key of availableKeys) {
                                    if (key.toLowerCase() === filename.toLowerCase()) {
                                        possibleFilenames.push(key);
                                        break;
                                    }
                                }
                                
                                // Find the first match
                                for (const possibleName of possibleFilenames) {
                                    if (list_of_chunks[possibleName]) {
                                        actualFilename = possibleName;
                                        break;
                                    }
                                }
                            }
                            
                            // Final check - if we still can't find the file, skip it
                            if (!list_of_chunks[actualFilename]) {
                                console.error("❌ Missing chunks data for filename:", filename);
                                console.log("📂 Available filenames in list_of_chunks:", Object.keys(list_of_chunks));
                                console.log("🔍 fileInfo object:", fileInfo);
                                return; // Skip this file if chunks are missing
                            }
                            
                            // Generate a unique ID for this chunk data to avoid inline JSON
                            const chunkDataId = 'chunk_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9) + '_' + index;
                            
                            // Store chunk data globally for retrieval
                            window.chunkDataStore = window.chunkDataStore || {};
                            window.chunkDataStore[chunkDataId] = {
                                chunkData: list_of_chunks[actualFilename] || {},
                                filename: actualFilename
                            };
                            
                            buttonsHtml += `
                                <div class="citation-button-wrapper" style="margin-bottom: 8px;">
                                    <button class="reference-button" 
                                            style="width: 100%; 
                                                   text-align: left; 
                                                   color: #2c3e50; 
                                                   background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(248,249,250,0.95)); 
                                                   font-weight: 600; 
                                                   font-size: 13px; 
                                                   border-radius: 8px; 
                                                   height: auto; 
                                                   min-height: 36px; 
                                                   padding: 8px 12px; 
                                                   border: 1px solid rgba(255,255,255,0.8); 
                                                   box-shadow: 0 1px 4px rgba(0,0,0,0.1);
                                                   transition: all 0.3s ease;
                                                   backdrop-filter: blur(10px);" 
                                            onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.15)'; this.style.borderColor='rgba(255,255,255,1)';" 
                                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 1px 4px rgba(0,0,0,0.1)'; this.style.borderColor='rgba(255,255,255,0.8)';"
                                            onclick='showPDFChunkReferenceById("${chunkDataId}")'>
                                        <div style="display: flex; align-items: center; justify-content: space-between;">
                                            <div style="display: flex; align-items: center; flex-grow: 1;">
                                                <div style="background: linear-gradient(135deg, ${fileInfo.impactColor || '#e3f2fd'}22, ${fileInfo.impactColor || '#e3f2fd'}11); 
                                                            border: 2px solid ${fileInfo.impactColor || '#000000'}44;
                                                            border-radius: 6px; 
                                                            padding: 4px; 
                                                            margin-right: 10px; 
                                                            display: flex; 
                                                            align-items: center; 
                                                            justify-content: center;
                                                            min-width: 24px;
                                                            min-height: 24px;
                                                            box-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4C7.34315 4 6 5.34315 6 7V17C6 18.6569 7.34315 20 9 20H15C16.6569 20 18 18.6569 18 17V10H15C13.3431 10 12 8.65685 12 7V4H9ZM14 5.41421L16.5858 8H15C14.4477 8 14 7.55228 14 7V5.41421ZM4 7C4 4.23858 6.23858 2 9 2H13C13.2652 2 13.5196 2.10536 13.7071 2.29289L19.7071 8.29289C19.8946 8.48043 20 8.73478 20 9V17C20 19.7614 17.7614 22 15 22H9C6.23858 22 4 19.7614 4 17V7ZM15.2071 12.2929C15.5976 12.6834 15.5976 13.3166 15.2071 13.7071L12.2071 16.7071C11.8166 17.0976 11.1834 17.0976 10.7929 16.7071L9.29289 15.2071C8.90237 14.8166 8.90237 14.1834 9.29289 13.7929C9.68342 13.4024 10.3166 13.4024 10.7071 13.7929L11.5 14.5858L13.7929 12.2929C14.1834 11.9024 14.8166 11.9024 15.2071 12.2929Z" fill="${fileInfo.impactColor || '#000000'}"/>
                                                    </svg>
                                                </div>
                                                <div style="flex-grow: 1;">
                                                    <div style="font-size: 13px; font-weight: 700; color: #2c3e50; margin-bottom: 2px; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">${actualFilename.replace('.pdf', '')}</div>
                                                    <div style="font-size: 11px; color: rgba(127,140,141,0.9); display: flex; align-items: center;">
                                                        <i class="fas fa-eye" style="margin-right: 4px; font-size: 8px;"></i>
                                                        • ${displayInfo}
                                                    </div>
                                                    ${fileInfo.llmExplanation && fileInfo.llmExplanation !== 'Not analyzed by LLM' ? `
                                                        <div style="font-size: 10px; color: rgba(0,128,0,0.8); margin-top: 3px; font-style: italic; line-height: 1.3;">
                                                            <i class="fas fa-robot" style="margin-right: 3px; font-size: 8px;"></i>
                                                            ${fileInfo.llmExplanation.length > 60 ? fileInfo.llmExplanation.substring(0, 60) + '...' : fileInfo.llmExplanation}
                                                        </div>
                                                    ` : ''}
                                                </div>
                                            </div>
                                            <div style="margin-left: 8px;">
                                                <i class="fas fa-chevron-right" style="color: rgba(0,0,0,0.7); font-size: 10px;"></i>
                                            </div>
                                        </div>
                                    </button>
                                </div>
                                `;
                        });


                        
                        messageContent += `
                            <div style="margin: 20px 0 15px 0;">
                                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                    <div style="background: linear-gradient(135deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1)); 
                                                border-radius: 10px; 
                                                padding: 8px; 
                                                margin-right: 12px; 
                                                display: flex; 
                                                align-items: center; 
                                                justify-content: center;">
                                        <i class="fas fa-file-pdf" style="color: rgba(255,255,255,0.9); font-size: 16px;"></i>
                                    </div>
                                    <div style="color: rgba(255,255,255,0.95); 
                                                font-size: 18px; 
                                                font-weight: 700; 
                                                text-shadow: 0 1px 3px rgba(0,0,0,0.3);">
                                        ${citationData.title || `References (${citationData.filesToDisplay.length})`}
                                    </div>
                                    <div style="flex-grow: 1; 
                                                height: 2px; 
                                                background: linear-gradient(90deg, rgba(255,255,255,0.3), transparent); 
                                                margin-left: 15px; 
                                                border-radius: 1px;">
                                    </div>
                                </div>
                                <div style="padding: 0 5px;">${buttonsHtml}</div>
                            </div>`;

                    }
                }

                var messageElement = $('<div>').addClass('chat-message ' + className);

                if (className.includes('bot-message')) {
                    messageElement.html(`
                        <div style="width: 100%; padding-top: 24px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
                            <div style="justify-content: flex-start; align-items: center; gap: 8px; display: inline-flex">
                                <div style="text-align: left; color: var(--primary-color); font-size: 16px; font-weight: 600; word-wrap: break-word;">
                    <img src="{{ url_for('static', filename='images/chat.png') }}" style="width: 16px; height: 16px; vertical-align: middle; margin-right: 6px;">
                                    Sapitor
                                </div>
                            </div>
                            <div style="padding: 16px 20px; background: var(--primary-color); border-radius: 16px; display: flex; flex-direction: column; gap: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.08);">
                                ${thinking ? `
                                    <div class="thinking-part" style="background: rgba(255,255,255,0.1); color: rgba(255,255,255,0.8); font-size: 14px; padding: 12px; border-radius: 8px; border: 1px solid rgba(255,255,255,0.1);">
                                        ${thinking.replace(/\n/g, '<br>')}
                                    </div>
                                ` : ''}
                                <div style="text-align: left; color: white; font-size: 16px; font-weight: 400; word-wrap: break-word; line-height: 1.5;">
                                    ${messageContent}
                                </div>
                            </div>
                        </div>
                    `);
                } else {
                    messageElement.html(`
                        <div style="width: 100%; padding-top: 24px; flex-direction: column; justify-content: flex-end; align-items: flex-end; gap: 8px; display: inline-flex">
                            <div style="text-align: right; color: #555; font-size: 16px; font-weight: 600; word-wrap: break-word;">
                                You <i class="fa-regular fa-circle-user" style="font-size: 14px; margin-left: 6px;"></i>
                            </div>
                            <div style="padding: 16px 20px; border-radius: 16px; background-color: var(--light-gray); box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                                <div style="color: var(--text-color); font-size: 16px; font-weight: 400; word-wrap: break-word; line-height: 1.5;">${messageContent}</div>
                            </div>
                        </div>
                    `);
                }

                $('#chatBox').append(messageElement);
                loadAllUsersChatHistory()
                $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);
                
                // Apply current theme to the newly added message
                setTimeout(() => {
                    const currentTheme = document.getElementById('theme').value || 'black';
                    updateThemeColors(currentTheme);
                }, 50);
                
                return [messageElement, null];
            }
            function adjustTextAreaHeight() {
                // Get the active textarea based on current language
                let activeTextArea;
                if (currentLanguage === 'en') {
                    activeTextArea = textArea;
                } else if (currentLanguage === 'zh-cn') {
                    activeTextArea = textAreaZhCn;
                } else if (currentLanguage === 'zh-tw') {
                    activeTextArea = textAreaZhTw;
                } else {
                    activeTextArea = textArea; // Default
                }
                
                activeTextArea.style.height = "0px";
                activeTextArea.style.height = Math.min(activeTextArea.scrollHeight, 150) + "px";

                inputContainer.style.height = "auto";
                outerContainer.style.height = "auto";

                var newHeight = Math.min(activeTextArea.scrollHeight + 20, 150) + "px";

                inputContainer.style.height = newHeight;
                outerContainer.style.height = newHeight;
            }

            // Add event listeners to all language textareas
            function addTextAreaEventListeners(textAreaElement) {
                textAreaElement.addEventListener('input', adjustTextAreaHeight);

                textAreaElement.addEventListener('paste', (event) => {
                    const pastedText = event.clipboardData.getData('text/plain');
                    event.preventDefault();
                    textAreaElement.value += pastedText;
                    adjustTextAreaHeight();
                });

                textAreaElement.addEventListener('keydown', (event) => {
                    if (event.key === 'Enter' && !event.shiftKey) {
                        event.preventDefault();
                        submitButton.click();
                        // Clear all textareas
                        textArea.value = '';
                        textAreaZhCn.value = '';
                        textAreaZhTw.value = '';
                        console.log('Message sent');
                    } else if (event.key === 'Enter' && event.shiftKey) {
                        event.preventDefault();
                        const cursorPosition = textAreaElement.selectionStart;
                        textAreaElement.value = textAreaElement.value.substring(0, cursorPosition) + '\n' + textAreaElement.value.substring(cursorPosition);
                        textAreaElement.selectionStart = textAreaElement.selectionEnd = cursorPosition + 1;
                        adjustTextAreaHeight();
                    }
                });
            }
            
            // Add event listeners to all textareas
            addTextAreaEventListeners(textArea);
            addTextAreaEventListeners(textAreaZhCn);
            addTextAreaEventListeners(textAreaZhTw);

            adjustTextAreaHeight();



            $('#chatForm').on('submit', function (e) {
                e.preventDefault();
                console.log("🚀 Form submitted!");
                
                // Get user message based on current language
                var userMessage;
                if (currentLanguage === 'en') {
                    userMessage = $('#user_input').val();
                } else if (currentLanguage === 'zh-cn') {
                    userMessage = $('#user_input_zh_cn').val();
                } else if (currentLanguage === 'zh-tw') {
                    userMessage = $('#user_input_zh_tw').val();
                }
                
                // Check if message is empty
                if (!userMessage || userMessage.trim() === '') {
                    alert('Please enter a message before sending.');
                    return;
                }
                
                var formData = new FormData(this);
                var ragStatus = $('#rag').val();
                var useOriginalText = $('#useOriginalText').is(':checked');
                formData.append('useOriginalText', useOriginalText);
                
                // Always send the message to the server with the 'user_input' field name
                formData.set('user_input', userMessage);
                
                appendMessage(userMessage, 'user-message', false);
                var [loaderMessage, ] = appendMessage('', 'bot-message', true);
                
                // Generate filename_list from selected files
                var filename_list = '';
                if (currentSelectedDataset && selectedFilesMap[currentSelectedDataset]) {
                    filename_list = selectedFilesMap[currentSelectedDataset].join(',');
                }
                formData.append('filename_list', filename_list)

                var includeHistory = $('#include_history').is(':checked');
                var maxHistoryNo = $('#max_history_no').val();
                var model = $('#model').val();
                var multifileStrategy = $('#multifile_strategy').val(); // 新增多文件策略参数

                formData.append('include_history', includeHistory);
                formData.append('max_history_no', maxHistoryNo);
                formData.append('model', model);
                formData.append('multifile_strategy', multifileStrategy); // 发送多文件策略到后端

                var selectedDataset = currentSelectedDataset;
                console.log("selectedDataset")
                console.log(model)
                if (ragStatus === 'on' && !selectedDataset) {

                    var warningWin = new bootstrap.Modal(document.getElementById('warningWin'), {
                        keyboard: false
                    });
                    warningWin.show();
                    formData.append('rag', 'off');

                } else {
                    formData.append('rag', ragStatus);
                }
                formData.append('selectedDataset', selectedDataset);
                

                
                // Clear all textareas
                document.getElementById('user_input').value = '';
                document.getElementById('user_input_zh_cn').value = '';
                document.getElementById('user_input_zh_tw').value = '';
                adjustTextAreaHeight();
                
                // Try streaming first, fallback to regular AJAX if streaming is not available
                if (typeof(EventSource) !== "undefined") {
                    // Use Server-Sent Events for streaming
                    handleStreamingResponse(formData, loaderMessage);
                } else {
                    // Fallback to regular AJAX
                    handleRegularResponse(formData, loaderMessage);
                }
            });

            // Function to handle streaming response
            function handleStreamingResponse(formData, loaderMessage) {
                fetch('/chat-stream', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Streaming not supported');
                    }
                    
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    
                    // Create streaming message element
                    loaderMessage.remove();
                    const [streamingMessage, messageContentDiv] = createStreamingMessage();
                    
                    let buffer = '';
                    let fullResponse = '';
                    let chunkList = null;
                    let contributionAnalysis = null;
                    
                    function readStream() {
                        return reader.read().then(({ done, value }) => {
                            if (done) {
                                // Stream completed, finalize the message
                                if (fullResponse.trim()) {
                                    finalizeStreamedMessage(messageContentDiv, fullResponse, chunkList, contributionAnalysis);
                                }
                                return;
                            }
                            
                            buffer += decoder.decode(value, { stream: true });
                            const lines = buffer.split('\n');
                            buffer = lines.pop(); // Keep incomplete line in buffer
                            
                            for (const line of lines) {
                                if (line.startsWith('data: ')) {
                                    try {
                                        const data = JSON.parse(line.slice(6));
                                        
                                        if (data.type === 'token') {
                                            // Add token to display
                                            fullResponse += data.content;
                                            updateStreamingMessage(messageContentDiv, fullResponse);
                                        } else if (data.type === 'metadata') {
                                            // Store metadata for final processing
                                            chunkList = data.chunk_list;
                                            contributionAnalysis = data.contribution_analysis;
                                        } else if (data.type === 'complete') {
                                            // Stream completed
                                            if (data.final_response) {
                                                fullResponse = data.final_response;
                                            }
                                            chunkList = data.chunk_list || chunkList;
                                            contributionAnalysis = data.contribution_analysis || contributionAnalysis;
                                        }
                                    } catch (e) {
                                        console.log('Non-JSON line:', line);
                                    }
                                }
                            }
                            
                            return readStream();
                        });
                    }
                    
                    return readStream();
                })
                .catch(error => {
                    console.log('Streaming failed, falling back to regular AJAX:', error);
                    // Fallback to regular AJAX
                    handleRegularResponse(formData, loaderMessage);
                });
            }

            // Function to handle regular AJAX response (fallback)
            function handleRegularResponse(formData, loaderMessage) {
                $.ajax({
                    url: '/chat',
                    type: 'post',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (data) {
                        loaderMessage.remove();
                        // Pass contribution analysis if available
                        const contributionAnalysis = data.contribution_analysis || null;
                        console.log("🤖 Received LLM reference analysis:", data.llm_reference_analysis_enabled);
                        console.log("🧠 Received semantic analysis:", data.semantic_analysis_enabled);
                        if (contributionAnalysis) {
                            console.log("📊 Contribution analysis data:", contributionAnalysis);
                            if (data.llm_reference_analysis_enabled) {
                                console.log("🎯 Using Two-Stage LLM Reference Analysis");
                            }
                        }
                        appendMessage(data.response, 'bot-message', false, data.chunk_list, contributionAnalysis);
                    },
                    error: function (xhr, status, error) {
                        console.error("❌ AJAX Error occurred:");
                        console.error("Status:", status);
                        console.error("Error:", error);
                        console.error("Response:", xhr.responseText);
                        console.error("Status Code:", xhr.status);
                        loaderMessage.find('.message-text').html('Error loading response. Check console for details.');
                    }
                });
            }

            // Function to create a streaming message element
            function createStreamingMessage() {
                const messageElement = $('<div>').addClass('chat-message bot-message');
                
                messageElement.html(`
                    <div style="width: 100%; padding-top: 24px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
                        <div style="justify-content: flex-start; align-items: center; gap: 8px; display: inline-flex">
                            <div style="text-align: left; color: var(--primary-color); font-size: 16px; font-weight: 600; word-wrap: break-word;">
                                <img src="{{ url_for('static', filename='images/chat.png') }}" style="width: 16px; height: 16px; vertical-align: middle; margin-right: 6px;">
                                Sapitor
                            </div>
                        </div>
                        <div style="padding: 16px 20px; background: var(--primary-color); border-radius: 16px; display: flex; flex-direction: column; gap: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.08);">
                            <div class="streaming-content" style="text-align: left; color: white; font-size: 16px; font-weight: 400; word-wrap: break-word; line-height: 1.5;">
                                <span class="cursor-blink">|</span>
                            </div>
                        </div>
                    </div>
                `);
                
                $('#chatBox').append(messageElement);
                $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);
                
                return [messageElement, messageElement.find('.streaming-content')];
            }

            // Function to update streaming message content
            function updateStreamingMessage(contentDiv, text) {
                // Parse markdown and update content
                const processedContent = marked.parse(text);
                contentDiv.html(processedContent + '<span class="cursor-blink">|</span>');
                
                // Auto-scroll to bottom
                $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);
            }

            // Function to finalize streamed message
            function finalizeStreamedMessage(contentDiv, fullText, chunkList, contributionAnalysis) {
                // Remove cursor
                contentDiv.find('.cursor-blink').remove();
                
                // Process final content with citations if available
                let finalContent = marked.parse(fullText);
                
                if (chunkList && Object.keys(chunkList).length > 0) {
                    console.log("📋 Adding citations to streamed message");
                    
                    // Generate citations (reuse existing logic)
                    let citationData = generateSmartCitations(chunkList, contributionAnalysis);
                    let buttonsHtml = '';
                    
                    citationData.filesToDisplay.forEach(function (fileInfo, index) {
                        const filename = fileInfo.filename;
                        const displayInfo = fileInfo.displayInfo;
                        
                        // Handle filename matching - try different variations
                        let actualFilename = filename;
                        if (!chunkList[filename]) {
                            // Try to find the correct filename by checking different variations
                            const possibleFilenames = [];
                            
                            // Case 1: filename ends with 'pdf' but not '.pdf' (like 'amberplacepdf')
                            if (filename.endsWith('pdf') && !filename.endsWith('.pdf')) {
                                // Insert dot before 'pdf': 'amberplacepdf' -> 'amberplace.pdf'
                                const withDot = filename.slice(0, -3) + '.pdf';
                                possibleFilenames.push(withDot);
                            }
                            
                            // Case 2: Try adding .pdf extension
                            if (!filename.endsWith('.pdf')) {
                                possibleFilenames.push(filename + '.pdf');
                            }
                            
                            // Case 3: Try removing .pdf extension if present
                            if (filename.endsWith('.pdf')) {
                                possibleFilenames.push(filename.replace('.pdf', ''));
                            }
                            
                            // Case 4: Try exact match with different case
                            const availableKeys = Object.keys(chunkList);
                            for (const key of availableKeys) {
                                if (key.toLowerCase() === filename.toLowerCase()) {
                                    possibleFilenames.push(key);
                                    break;
                                }
                            }
                            
                            // Find the first match
                            for (const possibleName of possibleFilenames) {
                                if (chunkList[possibleName]) {
                                    actualFilename = possibleName;
                                    break;
                                }
                            }
                        }
                        
                        // Final check - if we still can't find the file, skip it
                        if (!chunkList[actualFilename]) {
                            console.error("❌ Missing chunks data for filename:", filename);
                            return; // Skip this file if chunks are missing
                        }
                        
                        // Generate a unique ID for this chunk data
                        const chunkDataId = 'chunk_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9) + '_' + index;
                        
                        // Store chunk data globally
                        window.chunkDataStore = window.chunkDataStore || {};
                        window.chunkDataStore[chunkDataId] = {
                            chunkData: chunkList[actualFilename] || {},
                            filename: actualFilename
                        };
                        
                        buttonsHtml += `
                            <div class="citation-button-wrapper" style="margin-bottom: 8px;">
                                <button class="reference-button" 
                                        style="width: 100%; 
                                               text-align: left; 
                                               color: #2c3e50; 
                                               background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(248,249,250,0.95)); 
                                               font-weight: 600; 
                                               font-size: 13px; 
                                               border-radius: 8px; 
                                               height: auto; 
                                               min-height: 36px; 
                                               padding: 8px 12px; 
                                               border: 1px solid rgba(255,255,255,0.8); 
                                               box-shadow: 0 1px 4px rgba(0,0,0,0.1);
                                               transition: all 0.3s ease;
                                               backdrop-filter: blur(10px);" 
                                        onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.15)'; this.style.borderColor='rgba(255,255,255,1)';" 
                                        onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 1px 4px rgba(0,0,0,0.1)'; this.style.borderColor='rgba(255,255,255,0.8)';"
                                        onclick='showPDFChunkReferenceById("${chunkDataId}")'>
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <div style="display: flex; align-items: center; flex-grow: 1;">
                                            <div style="background: linear-gradient(135deg, ${fileInfo.impactColor || '#e3f2fd'}22, ${fileInfo.impactColor || '#e3f2fd'}11); 
                                                        border: 2px solid ${fileInfo.impactColor || '#000000'}44;
                                                        border-radius: 6px; 
                                                        padding: 4px; 
                                                        margin-right: 10px; 
                                                        display: flex; 
                                                        align-items: center; 
                                                        justify-content: center;
                                                        min-width: 24px;
                                                        min-height: 24px;
                                                        box-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4C7.34315 4 6 5.34315 6 7V17C6 18.6569 7.34315 20 9 20H15C16.6569 20 18 18.6569 18 17V10H15C13.3431 10 12 8.65685 12 7V4H9ZM14 5.41421L16.5858 8H15C14.4477 8 14 7.55228 14 7V5.41421ZM4 7C4 4.23858 6.23858 2 9 2H13C13.2652 2 13.5196 2.10536 13.7071 2.29289L19.7071 8.29289C19.8946 8.48043 20 8.73478 20 9V17C20 19.7614 17.7614 22 15 22H9C6.23858 22 4 19.7614 4 17V7ZM15.2071 12.2929C15.5976 12.6834 15.5976 13.3166 15.2071 13.7071L12.2071 16.7071C11.8166 17.0976 11.1834 17.0976 10.7929 16.7071L9.29289 15.2071C8.90237 14.8166 8.90237 14.1834 9.29289 13.7929C9.68342 13.4024 10.3166 13.4024 10.7071 13.7929L11.5 14.5858L13.7929 12.2929C14.1834 11.9024 14.8166 11.9024 15.2071 12.2929Z" fill="${fileInfo.impactColor || '#000000'}"/>
                                                </svg>
                                            </div>
                                            <div style="flex-grow: 1;">
                                                <div style="font-size: 13px; font-weight: 700; color: #2c3e50; margin-bottom: 2px; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">${actualFilename.replace('.pdf', '')}</div>
                                                <div style="font-size: 11px; color: rgba(127,140,141,0.9); display: flex; align-items: center;">
                                                    <i class="fas fa-eye" style="margin-right: 4px; font-size: 8px;"></i>
                                                    • ${displayInfo}
                                                </div>
                                            </div>
                                        </div>
                                        <div style="margin-left: 8px;">
                                            <i class="fas fa-chevron-right" style="color: rgba(0,0,0,0.7); font-size: 10px;"></i>
                                        </div>
                                    </div>
                                </button>
                            </div>
                        `;
                    });
                    
                    finalContent += `
                        <div style="margin: 20px 0 15px 0;">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <div style="background: linear-gradient(135deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1)); 
                                            border-radius: 10px; 
                                            padding: 8px; 
                                            margin-right: 12px; 
                                            display: flex; 
                                            align-items: center; 
                                            justify-content: center;">
                                    <i class="fas fa-file-pdf" style="color: rgba(255,255,255,0.9); font-size: 16px;"></i>
                                </div>
                                <div style="color: rgba(255,255,255,0.95); 
                                            font-size: 18px; 
                                            font-weight: 700; 
                                            text-shadow: 0 1px 3px rgba(0,0,0,0.3);">
                                    ${citationData.title || `References (${citationData.filesToDisplay.length})`}
                                </div>
                                <div style="flex-grow: 1; 
                                            height: 2px; 
                                            background: linear-gradient(90deg, rgba(255,255,255,0.3), transparent); 
                                            margin-left: 15px; 
                                            border-radius: 1px;">
                                </div>
                            </div>
                            <div style="padding: 0 5px;">${buttonsHtml}</div>
                        </div>
                    `;
                }
                
                contentDiv.html(finalContent);
                
                // Apply theme and scroll
                setTimeout(() => {
                    const currentTheme = document.getElementById('theme').value || 'black';
                    updateThemeColors(currentTheme);
                    $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);
                }, 50);
                
                // Save to chat history
                loadAllUsersChatHistory();
            }

            $('#chatBox').on('click', '.copy-btn', function () {
                var message = $(this).siblings('.message-text').text();
                navigator.clipboard.writeText(message).then(() => {
                    alert('Message copied!');
                });
            });

            $('#toggle-file-container').click(function () {
                $('.container').toggleClass('collapsed-right');
                var isCollapsed = $('.container').hasClass('collapsed-right');
                $(this).find('i').toggleClass('fa-arrow-left fa-arrow-right'); 
            });

            $('#toggle-database-icon-btn').click(function () {
                $('.container').toggleClass('collapsed-right');
            });

            $('#toggle-chat-history').click(function () {
                $('.container').toggleClass('collapsed-left');
                var isCollapsed = $('.container').hasClass('collapsed-left');
                $(this).find('i').toggleClass('fa-arrow-right fa-arrow-left'); 
            });

        function loadUserChatHistory(userId) {
            $.ajax({
                url: '/get-user-data/' + userId,
                type: 'GET',
                success: function (data) {
                    const chatBox = $('#chatBox');
                    chatBox.empty();
                    Object.entries(data).forEach(([key, entry]) => {
                        const uniqueId = `buttons-container-${key}`;
                        const userMessageHtml = `
                            <div style="width: 100%; padding-top: 20px; flex-direction: column; justify-content: flex-start; align-items: flex-end; gap: 6px; display: inline-flex">
                                <div style="padding-left: 2px; padding-right: 2px; padding-top: 1px; padding-bottom: 1px; justify-content: flex-end; align-items: center; gap: 10px; display: inline-flex">
                                    <div style="text-align: right; color: #000000; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 20px">You
                                        <i class="fa-regular fa-circle-user fa-lg" style="color: #000000; vertical-align: middle; position: relative; top: -3px;"></i>
                                    </div>
                                </div>
                                <div style="padding-left: 20px; padding-right: 20px; padding-top: 15px; padding-bottom: 15px; border-radius: 30px; overflow: hidden; border: 2px #000000 solid; justify-content: center; align-items: center; gap: 19px; display: inline-flex">
                                    <div style="color: #000000; font-size: 20px; font-weight: 700; word-wrap: break-word; font-size: 18px">${entry.q_msg}</div>
                                </div>
                            </div>
                        `;

                        // Clean message from any JSON data that might have leaked through
                        var cleanedMessage = entry.a_msg;
                        
                        // Remove any trailing contribution analysis JSON data that might have been appended
                        // Look for multiple patterns that indicate JSON data leakage
                        const jsonPatterns = [
                            /~<~,~>~<~score_distribution~>~<~:.*$/s,           // Original pattern
                            /~<~,~>~<~.*$/s,                                   // Any data starting with ~<~,~>~<~
                            /\}\)\'\s*style="background:.*$/s,                 // Button HTML with escaped JSON
                            /\}\"\)\'\s*style="background:.*$/s,               // Variation with quotes
                            /~<~,~>~<~llm_was_used~>~<~:.*$/s,                 // LLM analysis data
                            /~<~,~>~<~bbox~>~<~:.*$/s,                         // Bounding box data
                            /~<~,~>~<~chunk_index~>~<~:.*$/s,                  // Chunk index data
                            /~<~,~>~<~contribution_rank~>~<~:.*$/s,            // Contribution rank data
                            /References \(\d+ most relevant\)\s*~<~.*$/s,     // References section with JSON
                            /• Page \d+ • [^~]*~<~.*$/s                        // Page info with trailing JSON
                        ];
                        
                        for (const pattern of jsonPatterns) {
                            if (pattern.test(cleanedMessage)) {
                                console.log("🧹 Detected and removing leaked JSON/HTML data from chat history");
                                cleanedMessage = cleanedMessage.replace(pattern, '').trim();
                            }
                        }
                        
                        // Additional aggressive cleaning for embedded JSON in citation text
                        cleanedMessage = cleanedMessage.replace(/References \(\d+ most relevant\)\s*\n?[^a-zA-Z]*~<~.*$/s, 'References ($1 most relevant)');
                        
                        // Remove any standalone lines that are purely JSON data (start with ~<~)
                        cleanedMessage = cleanedMessage.replace(/^~<~.*$/gm, '');
                        
                        // Clean up any remaining empty lines or whitespace
                        cleanedMessage = cleanedMessage.replace(/\n\s*\n\s*\n/g, '\n\n').trim();
                        
                        var processedMessage = cleanedMessage.replace(/^\nAnswer: /, '').replace(/^AI:\s*/, '').trim();
                        const { thinking, finalAnswer } = parseMessage(processedMessage);

                        // Get current theme for dynamic background color
                        const currentTheme = document.getElementById('theme')?.value || 'black';
                        const themeBackgroundColor = currentTheme === 'teal' ? '#004F59' : '#000000';

                        const botMessageHtml = `
                            <div>
                                <div style="width: 100%; padding-top: 20px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 6px; display: inline-flex">
                                    <div style="justify-content: flex-start; align-items: center; gap: 5px; display: inline-flex">
                                        <div style="text-align: left; color: #000000; font-size: 20px; font-weight: 700; word-wrap: break-word;">
                                            <img src="{{ url_for('static', filename='images/chat.png') }}" style="width: 1.6em; height: 1.6em; vertical-align: middle; position: relative; top: -3px;">
                                            Sapitor
                                        </div>
                                    </div>
                                    <div style="padding: 15px 20px; background: ${themeBackgroundColor}; border-radius: 30px; display: flex; flex-direction: column; gap: 10px;">
                                        ${thinking ? `
                                            <div class="thinking-part" style="background: #f8f8f8; color: #666; font-size: 14px; padding: 10px; border-radius: 10px;">
                                                ${thinking.replace(/\n/g, '<br>')}
                                            </div>
                                        ` : ''}
                                        <div class="final-answer" style="text-align: justify; color: white; font-size: 18px; font-weight: 700; word-wrap: break-word;">
                                            ${marked.parse(finalAnswer)} 
                                        </div>
                                        ${entry.chunks_list && Object.keys(entry.chunks_list).length > 0 ? `
                                            <div style="margin: 20px 0 15px 0;">
                                                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                                    <div style="background: linear-gradient(135deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1)); 
                                                                border-radius: 10px; 
                                                                padding: 8px; 
                                                                margin-right: 12px; 
                                                                display: flex; 
                                                                align-items: center; 
                                                                justify-content: center;">
                                                        <i class="fas fa-file-pdf" style="color: rgba(255,255,255,0.9); font-size: 16px;"></i>
                                                    </div>
                                                    <div style="color: rgba(255,255,255,0.95); 
                                                                font-size: 18px; 
                                                                font-weight: 700; 
                                                                text-shadow: 0 1px 2px rgba(0,0,0,0.3);">
                                                        References (${Object.keys(entry.chunks_list).length} most relevant)
                                                    </div>
                                                </div>
                                                <div id="${uniqueId}"></div>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        `;

                        chatBox.append(userMessageHtml + botMessageHtml);

                        if (entry.chunks_list && Object.keys(entry.chunks_list).length > 0) {
                            // Use the same citation processing as new messages for consistency
                            let citationData = generateSmartCitations(entry.chunks_list, null);
                            
                            let buttonsHtml = '';
                            citationData.filesToDisplay.forEach(function (fileInfo, index) {
                                const filename = fileInfo.filename;
                                const displayInfo = fileInfo.displayInfo;
                                
                                // Handle filename matching - try different variations
                                let actualFilename = filename;
                                if (!entry.chunks_list[filename]) {
                                    // Try to find the correct filename by checking different variations
                                    const possibleFilenames = [];
                                    
                                    // Case 1: filename ends with 'pdf' but not '.pdf' (like 'amberplacepdf')
                                    if (filename.endsWith('pdf') && !filename.endsWith('.pdf')) {
                                        const withDot = filename.slice(0, -3) + '.pdf';
                                        possibleFilenames.push(withDot);
                                    }
                                    
                                    // Case 2: Try adding .pdf extension
                                    if (!filename.endsWith('.pdf')) {
                                        possibleFilenames.push(filename + '.pdf');
                                    }
                                    
                                    // Case 3: Try removing .pdf extension if present
                                    if (filename.endsWith('.pdf')) {
                                        possibleFilenames.push(filename.replace('.pdf', ''));
                                    }
                                    
                                    // Case 4: Try exact match with different case
                                    const availableKeys = Object.keys(entry.chunks_list);
                                    for (const key of availableKeys) {
                                        if (key.toLowerCase() === filename.toLowerCase()) {
                                            possibleFilenames.push(key);
                                            break;
                                        }
                                    }
                                    
                                    // Find the first match
                                    for (const possibleName of possibleFilenames) {
                                        if (entry.chunks_list[possibleName]) {
                                            actualFilename = possibleName;
                                            break;
                                        }
                                    }
                                }
                                
                                // Final check - if we still can't find the file, skip it
                                if (!entry.chunks_list[actualFilename]) {
                                    console.error("❌ Missing chunks data for filename:", filename);
                                    console.log("📂 Available filenames in entry.chunks_list:", Object.keys(entry.chunks_list));
                                    return; // Skip this file if chunks are missing
                                }
                                
                                // Generate a unique ID for this chunk data to avoid inline JSON
                                const chunkDataId = 'chunk_history_' + key + '_' + index + '_' + Math.random().toString(36).substr(2, 9);
                                
                                // Store chunk data globally for retrieval
                                window.chunkDataStore = window.chunkDataStore || {};
                                window.chunkDataStore[chunkDataId] = {
                                    chunkData: entry.chunks_list[actualFilename] || {},
                                    filename: actualFilename
                                };
                                
                                buttonsHtml += `
                                    <div class="citation-button-wrapper" style="margin-bottom: 8px;">
                                        <button class="reference-button" 
                                                style="width: 100%; 
                                                       text-align: left; 
                                                       color: #2c3e50; 
                                                       background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(248,249,250,0.95)); 
                                                       font-weight: 600; 
                                                       font-size: 13px; 
                                                       border-radius: 8px; 
                                                       height: auto; 
                                                       min-height: 36px; 
                                                       padding: 8px 12px; 
                                                       border: 1px solid rgba(255,255,255,0.8); 
                                                       box-shadow: 0 1px 4px rgba(0,0,0,0.1);
                                                       transition: all 0.3s ease;
                                                       backdrop-filter: blur(10px);" 
                                                onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.15)'; this.style.borderColor='rgba(255,255,255,1)';" 
                                                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 1px 4px rgba(0,0,0,0.1)'; this.style.borderColor='rgba(255,255,255,0.8)';"
                                                onclick='showPDFChunkReferenceById("${chunkDataId}")'>
                                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                                <div style="display: flex; align-items: center; flex-grow: 1;">
                                                    <div style="background: linear-gradient(135deg, ${fileInfo.impactColor || '#e3f2fd'}22, ${fileInfo.impactColor || '#e3f2fd'}11); 
                                                                border: 2px solid ${fileInfo.impactColor || '#000000'}44;
                                                                border-radius: 6px; 
                                                                padding: 4px; 
                                                                margin-right: 10px; 
                                                                display: flex; 
                                                                align-items: center; 
                                                                justify-content: center;
                                                                min-width: 24px;
                                                                min-height: 24px;
                                                                box-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4C7.34315 4 6 5.34315 6 7V17C6 18.6569 7.34315 20 9 20H15C16.6569 20 18 18.6569 18 17V10H15C13.3431 10 12 8.65685 12 7V4H9ZM14 5.41421L16.5858 8H15C14.4477 8 14 7.55228 14 7V5.41421ZM4 7C4 4.23858 6.23858 2 9 2H13C13.2652 2 13.5196 2.10536 13.7071 2.29289L19.7071 8.29289C19.8946 8.48043 20 8.73478 20 9V17C20 19.7614 17.7614 22 15 22H9C6.23858 22 4 19.7614 4 17V7ZM15.2071 12.2929C15.5976 12.6834 15.5976 13.3166 15.2071 13.7071L12.2071 16.7071C11.8166 17.0976 11.1834 17.0976 10.7929 16.7071L9.29289 15.2071C8.90237 14.8166 8.90237 14.1834 9.29289 13.7929C9.68342 13.4024 10.3166 13.4024 10.7071 13.7929L11.5 14.5858L13.7929 12.2929C14.1834 11.9024 14.8166 11.9024 15.2071 12.2929Z" fill="${fileInfo.impactColor || '#000000'}"/>
                                                        </svg>
                                                    </div>
                                                    <div style="flex-grow: 1;">
                                                        <div style="font-size: 13px; font-weight: 700; color: #2c3e50; margin-bottom: 2px; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">${actualFilename.replace('.pdf', '')}</div>
                                                        <div style="font-size: 11px; color: rgba(127,140,141,0.9); display: flex; align-items: center;">
                                                            <i class="fas fa-eye" style="margin-right: 4px; font-size: 8px;"></i>
                                                            • ${displayInfo}
                                                        </div>
                                                        ${fileInfo.llmExplanation && fileInfo.llmExplanation !== 'Not analyzed by LLM' ? `
                                                            <div style="font-size: 10px; color: rgba(0,128,0,0.8); margin-top: 3px; font-style: italic; line-height: 1.3;">
                                                                <i class="fas fa-robot" style="margin-right: 3px; font-size: 8px;"></i>
                                                                ${fileInfo.llmExplanation.length > 60 ? fileInfo.llmExplanation.substring(0, 60) + '...' : fileInfo.llmExplanation}
                                                            </div>
                                                        ` : ''}
                                                    </div>
                                                </div>
                                                <div style="margin-left: 8px;">
                                                    <i class="fas fa-chevron-right" style="color: rgba(0,0,0,0.7); font-size: 10px;"></i>
                                                </div>
                                            </div>
                                        </button>
                                    </div>
                                `;
                            });

                            document.getElementById(uniqueId).innerHTML = buttonsHtml;
                        }
                    });

                    $('#chatBox').scrollTop($('#chatBox')[0].scrollHeight);
                    
                    // Apply current theme to newly loaded content
                    setTimeout(() => {
                        const currentTheme = document.getElementById('theme')?.value || 'black';
                        updateThemeColors(currentTheme);
                    }, 100);
                },
                error: function (xhr, status, error) {
                    console.error("Failed to load user's chat history:", error);
                }
            });
        }

            $('#chatHistoryBox').on('click', '.chat-history-entry', function () {
                console.log("Clicked on: ", this);
                const userId = $(this).data('userid');
                console.log("test " + userId);
                if (userId) {
                    loadUserChatHistory(userId);
                    $('#chatHistoryBox .chat-history-entry').each(function () {
                        $(this).find('.delete-history-button').css('visibility', 'visible');
                    });
                    $(this).find('.delete-history-button').css('visibility', 'hidden');
                } else {
                    console.error("User ID is undefined.");
                }
            });

            $('#chatHistoryBox').on('click', '.chat-history-entry button', function () {
                var userId = $(this).closest('.chat-history-entry').data('userid');
                console.log("Deleting chat history for user ID: " + userId); 

                var that = this; 

                $.ajax({
                    url: '/delete-chat-history', 
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ userId: userId }),
                    success: function (response) {
                        console.log('Chat history deleted successfully for user ID: ' + userId);

                        $(that).closest('.chat-history-entry').remove();

                        loadAllUsersChatHistory();
                    },
                    error: function (xhr, status, error) {
                        console.error("Failed to delete chat history for user ID: " + userId, error);
                    }
                });
                return false; 
            });

            function newLoadInitialDatasets() {
                document.querySelector('.active-text').style.display = 'none';
                document.getElementById('backToDatabaseList').style.display = 'block';

                $.getJSON('/get-datasets', function (datasets) {
                    $('#databaseList').empty();
                    
                    // Sort datasets alphabetically by name
                    const sortedDatasets = [...datasets].sort((a, b) => {
                        return a.dataset_name.toLowerCase().localeCompare(b.dataset_name.toLowerCase());
                    });
                    
                    sortedDatasets.forEach(dataset => {
                        const datasetItem = $('<a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" style="background: #DDEFE8; border: 0px">').html(`
            <div class="me-2 d-flex align-items-center">
                <input class="form-check-input me-1" type="checkbox" value="" aria-label="..." style="margin-top: 0rem">
                <span class="fas fa-folder" style="color: #000000; margin-left: 0.5rem"></span>
            </div>
            <span class="flex-grow-1 dataset-name" style="cursor:pointer; color: #000000;        font-family: 'Inter' ,sans-serif; font-weight: 600">${dataset.dataset_name}</span>

        `);
                        datasetItem.appendTo('#databaseList');
                    });
                });
            }

            $('#databaseList').on('change', '.list-group-item .form-check-input', function () {
                var datasetName = $(this).closest('.list-group-item').find('.dataset-name').text().trim();
                if ($(this).is(':checked')) {

                    if (!selectedDatasets.includes(datasetName)) {
                        selectedDatasets.push(datasetName);
                    }
                } else {

                    selectedDatasets = selectedDatasets.filter(name => name !== datasetName);
                }
                console.log(selectedDatasets); 
            });

            $('#deleteButton').click(function () {
                if ($('#databaseList').is(':visible')) {

                    selectedDatasets.forEach(function (dataset) {
                        $.ajax({
                            url: '/delete-dataset',  
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({ dataset: dataset }),  
                            success: function (response) {
                                console.log(response.message);  

                                $('#databaseList .list-group-item').each(function () {
                                    if (selectedDatasets.includes($(this).find('.dataset-name').text().trim())) {
                                        $(this).remove();  
                                    }
                                });
                                selectedFiles = [];  
                            },
                            error: function (xhr, status, error) {
                                console.error("Error deleting dataset:", error);
                                selectedFiles = [];  
                            }
                        });
                    });
                } else if ($('#fileList').is(':visible')) {

                    $('#fileList .list-group-item .form-check-input:checked').each(function () {
                        var fileItem = $(this).closest('.list-group-item');  
                        var fileName = fileItem.find('.flex-grow-1').text().trim();
                        var selectedDataset = currentSelectedDataset;

                        $.ajax({
                            url: '/delete-file',  
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({ file_name: fileName, dataset: selectedDataset }),
                            success: function (response) {
                                console.log('File deleted successfully:', response.message);
                                fileItem.remove();  
                                selectedDataset = [];
                            },
                            error: function (xhr, status, error) {
                                console.error("Error deleting file:", error);
                            }
                        });
                    });
                }

            });

            function loadFilesForDataset(datasetName) {
                document.querySelector('.active-text').style.display = 'block';
                document.getElementById('backToDatabaseList').style.display = 'block';

                $('#fileList').empty();

                $('#fileList').append('<div class="d-flex justify-content-center align-items-center" style="position: fixed; top: 35%; left: 0; width: 100%; z-index: 9999;"><div class="docu-loader"></div></div>');
                $.getJSON('/get-datasets', function (datasets) {
                    const dataset = datasets.find(d => d.dataset_name === datasetName);
                    $('#fileList').empty();

                    $('#loading-icon').remove();

                    // Sort files alphabetically by name (create a copy to avoid modifying original)
                    const sortedFiles = [...dataset.document_list].sort((a, b) => {
                        return a.toLowerCase().localeCompare(b.toLowerCase());
                    });

                    sortedFiles.forEach(file => {

                        const isSelected = selectedFilesMap[datasetName] && selectedFilesMap[datasetName].includes(file);
                        const fileItem = $('<a href="#" class="list-group-item list-group-item-action d-flex align-items-center" style="background: #DDEFE8; border: 0px;">').html(`
                <div class="me-2 d-flex align-items-center">
                    <input class="form-check-input me-1" type="checkbox" value="" aria-label="..." style="margin-top: 0rem">
                    <span class="fas fa-file" style="color: #000000; margin-left: 0.5rem"></span>
                </div>
                <span class="flex-grow-1" style="color: #000000;         font-family: 'Inter' ,sans-serif; font-weight: 600">${file}</span>
                <label class="switch ms-auto select-switch"> 
                    <input type="checkbox" ${isSelected ? 'checked' : ''}>
                    <span class="slider round"></span>
                </label>
            `);
                        fileItem.appendTo('#fileList');

                        fileItem.find('.select-switch input[type="checkbox"]').on('click', function () {

                            Object.keys(selectedFilesMap).forEach(key => {
                                if (key !== datasetName) {
                                    selectedFilesMap[key] = [];
                                }
                            });

                            const isChecked = $(this).is(':checked');
                            const fileName = $(this).closest('.list-group-item').find('span.flex-grow-1').text();

                            if (!selectedFilesMap[datasetName]) {
                                selectedFilesMap[datasetName] = [];
                            }

                            if (isChecked) {
                                if (!selectedFilesMap[datasetName].includes(fileName)) {
                                    selectedFilesMap[datasetName].push(fileName);
                                }
                            } else {
                                selectedFilesMap[datasetName] = selectedFilesMap[datasetName].filter(f => f !== fileName);
                            }

                            $.ajax({
                                url: '/selected-files',
                                type: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify({ selectedFiles: selectedFilesMap[datasetName] }),
                                success: function (response) {
                                    console.log('Selected files sent successfully');
                                },
                                error: function (xhr, status, error) {
                                    console.log('Error: ' + error);
                                }
                            });
                        });
                    });
                });
            }

            $('#fileList').on('change', '.form-check-input', function () {
                const isChecked = $(this).is(':checked');
                const fileName = $(this).closest('.list-group-item').find('.flex-grow-1').text().trim(); 

                if (isChecked) {

                    if (!selectedFiles.includes(fileName)) {
                        selectedFiles.push(fileName);
                    }
                } else {

                    selectedFiles = selectedFiles.filter(name => name !== fileName);
                }

                console.log(selectedFiles);
            });

            $('#confirmCreateFolder').on('click', function () {
                var originalInput = $('#newFolderName').val().trim();
                var sanitizedInput = originalInput.replace(/[^a-zA-Z0-9_]/g, '_').replace(/^_+/, '');

                if (sanitizedInput) {
                    if (originalInput !== sanitizedInput) {
                        console.log(`Dataset name sanitized: "${originalInput}" -> "${sanitizedInput}"`);
                    }

                    // 直接在前端添加数据集项，使用与现有数据集相同的样式
                    const datasetItem = $('<a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" style="background: #DDEFE8; border: 0px">')
                        .html(`
                <div class="me-2 d-flex align-items-center">
                     <input class="form-check-input me-1" type="checkbox" value="" aria-label="..." style="margin-top: 0rem">
                    <span class="fas fa-folder" style="color: #000000; margin-left: 0.5rem"></span>
                </div>
                <span class="flex-grow-1 dataset-name" style="cursor:pointer; color: #000000; font-family: 'Inter' ,sans-serif; font-weight: 600;">${sanitizedInput}</span>
            `);

                    // 添加到数据集列表
                    $('#databaseList').append(datasetItem);

                    // 清空输入框并关闭模态框
                    $('#newFolderName').val('');
                    $('#createFolderModal').modal('hide');

                    console.log(`✅ 数据集 "${sanitizedInput}" 已添加到列表`);
                } else {
                    alert('Please enter a dataset name.');
                }
            });

            $('#databaseList').on('click', '.dataset-name', function () {
                const dataset = $(this).text();
                currentSelectedDataset = dataset;
                loadFilesForDataset(dataset);
                $('#databaseList').hide();
                $('#fileList').show();
                updateButtonVisibility(); 
            });

            $('#backToDatabaseList').click(function () {
                document.getElementById('backToDatabaseList').style.display = 'none';

                document.querySelector('.active-text').style.display = 'none';
                $('#fileList').hide();
                $('#databaseList').show();
                updateButtonVisibility(); 
            });

            $('#createFolderButton').on('click', function () {
                var createFolderModal = new bootstrap.Modal(document.getElementById('createFolderModal'), {
                    keyboard: false
                });
                createFolderModal.show();
            });

            function updateButtonVisibility() {
                if ($('#fileList').is(':visible')) {

                    $('#createFolderButton').hide();
                    $('#uploadFileButton').show();
                } else {

                    $('#createFolderButton').show();
                    $('#uploadFileButton').hide();
                }
            }

            function addDatasetToLoaded(dataset) {
                if (dataset && $('#loaded-dataset-selector').find('#dataset-' + dataset).length == 0) {
                    var datasetDiv = $('<div>').attr('id', 'dataset-' + dataset).addClass('loaded-dataset-item');
                    datasetDiv.html('<span class="dataset-name">' + dataset + '</span><button class="delete-dataset-btn">x</button>');
                    $('#loaded-dataset-selector').append(datasetDiv);
                }
            }

            function updateFileList(selectedDataset) {
                if (!selectedDataset) {
                    $('#fileList').empty();

                    return;
                }
                $.getJSON('../vectorstore/vsdb_log.json', function (data) {
                    var filteredFiles = data.filter(function (file) {
                        return file.dataset === selectedDataset;
                    });

                    // Sort files alphabetically by filename
                    filteredFiles.sort(function (a, b) {
                        return a.filename.toLowerCase().localeCompare(b.filename.toLowerCase());
                    });

                    $('#fileList').empty();

                    filteredFiles.forEach(function (file) {
                        var fileItem = $('<div>').addClass('file-item');
                        fileItem.html('<input type="checkbox"><span class="file-name">' + file.filename + '</span><button class="delete-btn">Delete</button>');

                        $('#fileList').append(fileItem);
                    });
                    updateFileCount();
                });
            }

            updateFileList($('#dataset-selector').val());

            $('#uploadFileButton').on('click', function () {
                $('#file-upload').click();
            });

            $('#file-upload').on('change', function () {
                if (this.files.length > 0) {
                    var formData = new FormData();
                    for (var i = 0; i < this.files.length; i++) {
                        formData.append('file', this.files[i]);
                    }
                    var selectedDataset = currentSelectedDataset;
                    formData.append('selectedDataset', selectedDataset);

                    var uploadLoading = document.getElementById("uploadLoading");
                    console.log("file-upload1");

                    var xhr = new XMLHttpRequest();
                    xhr.open('POST', '/upload', true);

                    xhr.upload.onprogress = function (e) {
                        console.log("file-upload2");

                        uploadLoading.style.visibility = "visible";
                        if (e.lengthComputable) {
                            var percentComplete = (e.loaded / e.total) * 100;
                            $('#upload-progress-bar').css('width', percentComplete + '%');

                            if (percentComplete === 100) {
                                $('#upload-status-message').text('Processing file...');
                            }
                        }
                    };

                    xhr.onload = function () {
                        if (xhr.status === 200) {
                            $('#upload-status-message').text('File uploaded successfully');
                            $('#upload-loader .loader').hide();

                            uploadLoading.style.visibility = "hidden";

                            // Reload the file list to maintain alphabetical order
                            loadFilesForDataset(currentSelectedDataset);
                        } else {

                            uploadLoading.style.visibility = "hidden";

                            $('#upload-status-message').text('Error occurred during file upload');
                            $('#upload-loader .loader').hide();
                        }
                        $('#file-upload').val('');
                    }.bind(this);

                    xhr.onerror = function () {

                        uploadLoading.style.visibility = "hidden";

                        $('#upload-status-message').text('Network error occurred during file upload');
                        $('#upload-loader .loader').hide();
                        $('#file-upload').val('');
                    };

                    xhr.send(formData);
                }
            });

            $('[data-bs-toggle="offcanvas"]').on('click', function () {
                $(this).toggleClass('collapsed');
            });

            $('#fileList').on('click', '.delete-switch', function () {
                var fileName = $(this).siblings('.flex-grow-1').text(); 
                var selectedDataset = $('#initial-dataset-selector').val(); 

                $.ajax({
                    url: '/delete-file',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ file_name: fileName, dataset: selectedDataset }),
                    success: function (response) {
                        console.log(response.message);
                    },
                    error: function (xhr, status, error) {
                        console.log("Error: " + error);
                    }
                });

                $(this).closest('.list-group-item').remove(); 
            });

            function updateDeleteButtonStatus() {
                var selectedDataset = $('#initial-dataset-selector').val();
                if (selectedDataset) {
                    $('#delete-dataset-btn').css('background-color', '#f44336').prop('disabled', false);
                } else {
                    $('#delete-dataset-btn').css('background-color', 'grey').prop('disabled', true);
                }
            }

            $('#delete-dataset-btn').show();

            $(document).on('click', '.loaded-dataset-item', function () {
                var selectedDataset = $(this).find('.dataset-name').text();
                updateFileList(selectedDataset);
                updateFileCount();
                $('#fileList').empty();

                $.ajax({
                    url: '/selected-dataset',
                    type: 'POST',
                    contentType: 'application/json',

                    data: JSON.stringify({ selectedDataset: selectedDataset }),
                    success: function (response) {
                        console.log('Selected dataset updated successfully:', response.message);
                    },
                    error: function (xhr, status, error) {
                        console.log("Error occurred while updating selected dataset:", error);
                    }
                });
            });

            $('#fileList').on('click', '.delete-btn', function () {
                var fileName = $(this).siblings('.file-name').text();
                var selectedDataset = $('#initial-dataset-selector').val(); 

                $.ajax({
                    url: '/delete-file',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ file_name: fileName, dataset: selectedDataset }), 
                    success: function (response) {
                        console.log(response.message);
                    },
                    error: function (xhr, status, error) {
                        console.log("Error: " + error);
                    }
                });

                $(this).parent('.file-item').remove();
            });

            function updateModelOptions() {
                console.log("updateModelOptions")
                var modeSelect = document.getElementById("mode");
                var modelSelect = document.getElementById("model");

                console.log(modelSelect.value)
                console.log(modelSelect.value)

                if (modeSelect.value === "online") {
                    modelSelect.options[0].style.display = "block"; 
                    modelSelect.options[1].style.display = "none"; 

                    modelSelect.value = "gpt-3.5";
                } else {
                    modelSelect.options[0].style.display = "none"; 
                    modelSelect.options[1].style.display = "block"; 

                    modelSelect.value = "mixtral";
                }
            }

            updateModelOptions();
            $('#mode').change(updateModelOptions);

            function updateFormValues() {
                var mode = $('#mode').val();
                var model = $('#model').val();
                var rag = $('#rag').val();

                $('#form_mode').val(mode);
                $('#form_model').val(model);
                $('#form_rag').val(rag);

                $('#display-mode').text(mode);
                $('#display-model').text(model);
                $('#display-rag').text(rag);
            }

            $('#mode, #model, #rag').change(updateFormValues);

            function loadInstructOptions() {
                $.ajax({
                    url: '/get-prompt-templates',
                    type: 'GET',
                    success: function (data) {
                        var promptTemplates = data.prompt_template_list;
                        var instructSelector = $('#instruct-selector');
                        instructSelector.empty(); 

                        instructSelector.append($('<option>', {
                            value: 'none',
                            text: 'No role selected'
                        }));

                        promptTemplates.forEach(function (template) {
                            instructSelector.append($('<option>', {
                                value: template,
                                text: template
                            }));
                        });
                    },
                    error: function () {
                        console.log('Error loading role options');
                    }
                });
            }

            $('#select-all-files').change(function () {
                var isChecked = $(this).is(':checked');
                $('#fileList .file-item input[type="checkbox"]').prop('checked', isChecked);

                if (isChecked) {
                    var selectedFiles = [];
                    $('#fileList .file-item input[type="checkbox"]:checked').each(function () {
                        selectedFiles.push($(this).siblings('.file-name').text());
                    });

                    $.ajax({
                        url: '/selected-files',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ selectedFiles: selectedFiles }),
                        success: function (response) {
                            console.log('Selected files sent successfully', response);
                        },
                        error: function (xhr, status, error) {
                            console.error('Error: ' + error);
                        }
                    });
                }
            });
            $('#toggle-options-btn').click(function () {
                $('.options-container').slideToggle(); 
            });
            $('#initial-dataset-selector').change(function () {
                var selectedDataset = $(this).val();
                updateFileList(selectedDataset)
                updateDeleteButtonStatus();
            });

            $('#delete-dataset-btn').click(function () {
                var selectedDataset = $('#initial-dataset-selector').val();
                if (selectedDataset) {
                    $.ajax({
                        url: '/delete-dataset',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ dataset: selectedDataset }), 
                        success: function (response) {
                            console.log('Dataset deleted successfully:', response);
                            loadInitialDatasets();
                            $('#initial-dataset-selector').val('');
                            $('#fileList').empty();
                            updateFileCount();
                        },
                        error: function (xhr, status, error) {
                            console.log("Error occurred while deleting dataset:", error);
                        }
                    });
                } else {
                    alert('Please select a dataset to delete.');
                }
            });
            $('#toggle-new-dataset-btn').click(function () {
                $('#new-dataset-container').slideToggle();
            });

            $('#create-dataset-btn').click(function () {
                var newDatasetName = $('#new-dataset-name').val().trim();
                if (newDatasetName) {
                    $('#initial-dataset-selector').append('<option value="' + newDatasetName + '">' + newDatasetName + '</option>');
                    $('#initial-dataset-selector').val(newDatasetName);
                    $('#new-dataset-name').val('');
                    $('#new-dataset-container').slideUp();
                    var selectedDataset = $(this).val();
                    updateFileList(selectedDataset);
                    updateDeleteButtonStatus();
                } else {
                    alert('Please enter a dataset name.');
                }
            });
            $('#addChatHistoryButton').click(function () {

                $.ajax({
                    url: '/create-new-chat-history',
                    type: 'POST',
                    success: function (response) {

                        console.log('New chat history added successfully:', response);

                        loadAllUsersChatHistory();

                        setTimeout(function () {
                            $('#chatHistoryBox .chat-history-entry:first-child').click(); 
                        }, 1000); 
                    },
                    error: function (xhr, status, error) {
                        console.error("Error creating new chat history:", error);
                    }
                });
            });

            $(document).on('click', '.chat-history-entry', function () {
                const userId = $(this).data('userid');
                console.log("User ID: ", userId);

                $(this).css('background-color', '#e0e0e0'); 
                setTimeout(() => {
                    $(this).css('background-color', ''); 
                }, 2000); 
            });
            $(document).on('click', '.chat-history-entry', function () {
                const userId = $(this).data('userid');

                $('#feedback').text('Loading user info...'); 

                setTimeout(() => {
                    $('#feedback').text(''); 

                }, 2000); 
            });
            $(document).on('click', '.chat-history-entry', function () {
                const userId = $(this).data('userid');
                $.ajax({
                    url: '/update-user-session',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ userId: userId }),
                    success: function (response) {
                        console.log('Send UserID Successfully', response);
                    },
                    error: function (xhr, status, error) {
                        console.error("Send UserID error", error);
                    }
                });
            });

        });

        // Function removed - citations now limited to top 5 references only
        function showAllCitations(allChunksData, contributionAnalysisData = null) {
            console.log("showAllCitations function disabled - only top 5 references are now displayed");
            return; // Function disabled
            
            try {
                // Parse the data
                const chunksData = JSON.parse(allChunksData.replaceAll("~>~<~", '"'));
                console.log("Parsed chunks data:", chunksData);
                
                // Parse contribution analysis if provided
                let contributionAnalysis = null;
                if (contributionAnalysisData && contributionAnalysisData !== 'null') {
                    contributionAnalysis = JSON.parse(contributionAnalysisData.replaceAll("~>~<~", '"'));
                    console.log("Parsed contribution analysis:", contributionAnalysis);
                }
                
                // Remove existing modal if it exists
                $('#allCitationsModal').remove();
                
                // Create a modal dialog to display all citations
                const modalHtml = `
                <div class="modal fade" id="allCitationsModal" tabindex="-1" aria-labelledby="allCitationsModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content" style="border-radius: 15px; border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                            <div class="modal-header" style="background: linear-gradient(135deg, #000000, #1A1A1A); color: white; border-top-left-radius: 15px; border-top-right-radius: 15px; border-bottom: none;">
                                <h5 class="modal-title" id="allCitationsModalLabel" style="font-weight: bold; font-size: 1.4rem;">
                                    <i class="fas fa-book-open" style="margin-right: 10px;"></i>Relevant Citations
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body" id="allCitationsBody" style="max-height: 70vh; overflow-y: auto; padding: 20px; background: #f8f9fa;">
                                <!-- Citation buttons will be inserted here -->
                            </div>
                            <div class="modal-footer" style="border-top: 1px solid #e9ecef; background: #f8f9fa; border-bottom-left-radius: 15px; border-bottom-right-radius: 15px;">
                                <small class="text-muted">Showing citations ranked by relevance to your question. Click any citation to view the document.</small>
                            </div>
                        </div>
                    </div>
                </div>
                `;
                
                // Append the modal to the body
                $('body').append(modalHtml);
                
                // Generate buttons for all files
                let allButtonsHtml = '';
                const filenames = Object.keys(chunksData);
                
                // Check if we have semantic contribution data and process it properly
                let fileContributionData = {};
                let relevantFilesOnly = [];
                
                if (contributionAnalysis && contributionAnalysis.chunks_by_contribution) {
                    // Only process chunks that meet minimum relevance criteria
                    ['high', 'medium'].forEach(level => { // Note: excluding 'low' for more selective citations
                        if (contributionAnalysis.chunks_by_contribution[level]) {
                            contributionAnalysis.chunks_by_contribution[level].forEach(chunk => {
                                let filename = chunk.source;
                                
                                // Handle filename variations (.pdf extension issues)
                                if (!filename.endsWith('.pdf')) {
                                    if (filename.endsWith('pdf')) {
                                        filename = filename.slice(0, -3) + '.pdf';
                                    } else {
                                        filename += '.pdf';
                                    }
                                }
                                
                                if (!fileContributionData[filename]) {
                                    fileContributionData[filename] = { 
                                        high: 0, medium: 0, low: 0, 
                                        totalScore: 0, avgScore: 0, count: 0,
                                        relevantPages: new Set(),
                                        chunks: []
                                    };
                                }
                                fileContributionData[filename][level]++;
                                fileContributionData[filename].totalScore += chunk.contribution_score || 0.5;
                                fileContributionData[filename].count++;
                                fileContributionData[filename].relevantPages.add(chunk.page_num);
                                fileContributionData[filename].chunks.push(chunk);
                            });
                        }
                    });
                    
                    // Also include some low-impact chunks but only if they score above threshold
                    if (contributionAnalysis.chunks_by_contribution['low']) {
                        contributionAnalysis.chunks_by_contribution['low'].forEach(chunk => {
                            // Only include low-impact chunks with scores above 0.4 (40%)
                            if ((chunk.contribution_score || 0.3) > 0.4) {
                                let filename = chunk.source;
                                
                                // Handle filename variations
                                if (!filename.endsWith('.pdf')) {
                                    if (filename.endsWith('pdf')) {
                                        filename = filename.slice(0, -3) + '.pdf';
                                    } else {
                                        filename += '.pdf';
                                    }
                                }
                                
                                if (!fileContributionData[filename]) {
                                    fileContributionData[filename] = { 
                                        high: 0, medium: 0, low: 0, 
                                        totalScore: 0, avgScore: 0, count: 0,
                                        relevantPages: new Set(),
                                        chunks: []
                                    };
                                }
                                fileContributionData[filename]['low']++;
                                fileContributionData[filename].totalScore += chunk.contribution_score || 0.5;
                                fileContributionData[filename].count++;
                                fileContributionData[filename].relevantPages.add(chunk.page_num);
                                fileContributionData[filename].chunks.push(chunk);
                            }
                        });
                    }
                    
                    // Calculate average scores and filter files
                    Object.keys(fileContributionData).forEach(filename => {
                        const data = fileContributionData[filename];
                        data.avgScore = data.count > 0 ? data.totalScore / data.count : 0.5;
                        
                        // Only include files with meaningful contribution
                        if (data.count > 0 && (data.high > 0 || data.medium > 0 || data.avgScore > 0.4)) {
                            relevantFilesOnly.push(filename);
                        }
                    });
                } else {
                    // Fallback: use all available files but mark as low relevance
                    relevantFilesOnly = Object.keys(chunksData);
                }
                
                if (relevantFilesOnly.length === 0) {
                    allButtonsHtml = '<div class="text-center text-muted"><i class="fas fa-exclamation-circle"></i> No relevant citations found for this question</div>';
                } else {
                    console.log(`📊 Showing ${relevantFilesOnly.length} relevant files out of ${Object.keys(chunksData).length} total files`);
                    // Sort files by semantic contribution (highest avg score first)
                    const sortedFilenames = relevantFilesOnly.sort((a, b) => {
                        const scoreA = fileContributionData[a]?.avgScore || 0.3;
                        const scoreB = fileContributionData[b]?.avgScore || 0.3;
                        return scoreB - scoreA; // Descending order
                    });
                    
                    sortedFilenames.forEach(function(filename, index) {
                        // Only show files that have semantic contribution data or use filtered count
                        let relevantChunkCount = 0;
                        let relevantPageCount = 0;
                        let displayText = '';
                        
                        if (fileContributionData[filename] && fileContributionData[filename].count > 0) {
                            // Use semantic analysis data - only count relevant chunks
                            const contrib = fileContributionData[filename];
                            relevantChunkCount = contrib.high + contrib.medium + contrib.low;
                            relevantPageCount = contrib.relevantPages.size; // Actual count of relevant pages
                            
                            displayText = `${relevantChunkCount} relevant citation${relevantChunkCount > 1 ? 's' : ''} across ${relevantPageCount} page${relevantPageCount > 1 ? 's' : ''}`;
                            
                            let semanticInfo = [];
                            // Show actual semantic analysis results
                            if (contrib.high > 0) semanticInfo.push(`${contrib.high} high impact`);
                            if (contrib.medium > 0) semanticInfo.push(`${contrib.medium} medium`);
                            if (contrib.low > 0) semanticInfo.push(`${contrib.low} low impact`);
                            
                            // Add average relevance score
                            const avgPercent = Math.round(contrib.avgScore * 100);
                            
                            if (semanticInfo.length > 0) {
                                displayText += ` • ${semanticInfo.join(', ')} (${avgPercent}% relevance)`;
                            } else {
                                displayText += ` • ${avgPercent}% relevance`;
                            }
                        } else {
                            // Fallback: if no semantic data, show minimal info
                            let fileChunkCount = 0;
                            Object.keys(chunksData[filename]).forEach(function(pageNum) {
                                fileChunkCount += chunksData[filename][pageNum].length;
                            });
                            
                            relevantChunkCount = Math.max(1, Math.floor(fileChunkCount * 0.3)); // Assume 30% relevant
                            relevantPageCount = Math.max(1, Math.floor(Object.keys(chunksData[filename]).length * 0.5)); // Assume 50% pages relevant
                            displayText = `${relevantChunkCount} citation${relevantChunkCount > 1 ? 's' : ''} across ${relevantPageCount} page${relevantPageCount > 1 ? 's' : ''} • Estimated relevance`;
                        }
                        
                        allButtonsHtml += `
                        <div class="citation-item mb-3" style="transition: all 0.3s ease;">
                            <button class="citation-button w-100" 
                                    style="text-align: left; 
                                           color: #2c3e50; 
                                           background: linear-gradient(135deg, #ffffff, #f8f9fa); 
                                           font-weight: 600; 
                                           font-size: 15px; 
                                           border-radius: 12px; 
                                           height: auto; 
                                           padding: 15px; 
                                           border: 2px solid #e3f2fd; 
                                           box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                                           transition: all 0.3s ease;"
                                    onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 16px rgba(0,0,0,0.15)'; this.style.borderColor='#000000';"
                                    onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 8px rgba(0,0,0,0.1)'; this.style.borderColor='#e3f2fd';"
                                    onclick='showPDFChunkReference("${JSON.stringify(chunksData[filename]).replaceAll('"', "~>~<~")}", "${filename}"); $("#allCitationsModal").modal("hide");'>
                                <div style="display: flex; align-items: center; justify-content: space-between;">
                                    <div style="display: flex; align-items: center; flex-grow: 1;">
                                        <div style="background: linear-gradient(135deg, #000000, #1A1A1A); 
                                                    border-radius: 10px; 
                                                    padding: 8px; 
                                                    margin-right: 15px; 
                                                    display: flex; 
                                                    align-items: center; 
                                                    justify-content: center;
                                                    min-width: 40px;
                                                    min-height: 40px;">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4C7.34315 4 6 5.34315 6 7V17C6 18.6569 7.34315 20 9 20H15C16.6569 20 18 18.6569 18 17V10H15C13.3431 10 12 8.65685 12 7V4H9ZM14 5.41421L16.5858 8H15C14.4477 8 14 7.55228 14 7V5.41421ZM4 7C4 4.23858 6.23858 2 9 2H13C13.2652 2 13.5196 2.10536 13.7071 2.29289L19.7071 8.29289C19.8946 8.48043 20 8.73478 20 9V17C20 19.7614 17.7614 22 15 22H9C6.23858 22 4 19.7614 4 17V7ZM15.2071 12.2929C15.5976 12.6834 15.5976 13.3166 15.2071 13.7071L12.2071 16.7071C11.8166 17.0976 11.1834 17.0976 10.7929 16.7071L9.29289 15.2071C8.90237 14.8166 8.90237 14.1834 9.29289 13.7929C9.68342 13.4024 10.3166 13.4024 10.7071 13.7929L11.5 14.5858L13.7929 12.2929C14.1834 11.9024 14.8166 11.9024 15.2071 12.2929Z" fill="white"/>
                                            </svg>
                                        </div>
                                        <div style="flex-grow: 1;">
                                            <div style="font-size: 16px; font-weight: 700; color: #2c3e50; margin-bottom: 5px;">${filename}</div>
                                            <div style="font-size: 13px; color: #7f8c8d; display: flex; align-items: center;">
                                                <i class="fas fa-quote-left" style="margin-right: 5px; color: #000000;"></i>
                                                ${displayText}
                                            </div>
                                        </div>
                                    </div>
                                    <div style="margin-left: 15px;">
                                        <i class="fas fa-external-link-alt" style="color: #000000; font-size: 14px;"></i>
                                    </div>
                                </div>
                            </button>
                        </div>
                        `;
                    });
                }
                
                // Update the modal content
                $('#allCitationsBody').html(allButtonsHtml);
                
                // Show the modal
                const modalElement = document.getElementById('allCitationsModal');
                const modal = new bootstrap.Modal(modalElement, {
                    backdrop: true,
                    keyboard: true,
                    focus: true
                });
                modal.show();
                
                console.log("Modal should be visible now");
                
            } catch (error) {
                console.error("Error in showAllCitations:", error);
                alert("Error displaying citations. Please check the console for details.");
            }
        }

        // =============================================================================
        // SMART CITATION PROCESSING
        // =============================================================================
        
        function generateSmartCitations(list_of_chunks, contribution_analysis) {
            console.log("🤖 Generating smart citations with LLM reference analysis support");
            
            let referencesToDisplay = [];
            let totalFiles = Object.keys(list_of_chunks).length;
            
            // If we have contribution analysis (either LLM-based or semantic), use it for intelligent ranking
            if (contribution_analysis && contribution_analysis.analysis_summary) {
                const analysisMethod = contribution_analysis.analysis_summary.analysis_method || 'unknown';
                console.log(`📊 Using ${analysisMethod} analysis for citation ranking - showing individual chunks`);
                
                // Collect all chunks with their contribution scores
                let allChunks = [];
                
                // Process all contribution levels and collect individual chunks
                ['high', 'medium', 'low'].forEach(level => {
                    if (contribution_analysis.chunks_by_contribution[level]) {
                        contribution_analysis.chunks_by_contribution[level].forEach(chunk => {
                            allChunks.push({
                                filename: chunk.source,
                                page_num: chunk.page_num,
                                contribution_score: chunk.contribution_score,
                                contribution_rank: chunk.contribution_rank,
                                content_type: chunk.content_type,
                                level: level
                            });
                        });
                    }
                });
                
                // Sort all chunks by contribution score (highest first)
                allChunks.sort((a, b) => b.contribution_score - a.contribution_score);
                
                console.log("📊 All chunks sorted by relevance:", allChunks);
                
                // Group chunks by file for better display, but maintain individual ranking
                let fileChunkGroups = {};
                let displayedChunks = 0;
                const maxReferences = 5; // Show top 5 most relevant references
                
                for (const chunk of allChunks) {
                    if (displayedChunks >= maxReferences) break;
                    
                    const filename = chunk.filename;
                    
                    // Create a unique reference for this specific chunk
                    const chunkId = `${filename}_page${chunk.page_num}_${chunk.contribution_rank}`;
                    
                    let impactLevel = '';
                    let impactColor = '';
                    
                    if (chunk.contribution_score >= 0.7) {
                        impactLevel = 'High Impact';
                        impactColor = '#28a745';
                    } else if (chunk.contribution_score >= 0.4) {
                        impactLevel = 'Medium Impact';
                        impactColor = '#fd7e14';
                    } else {
                        impactLevel = 'Low Impact';
                        impactColor = '#6c757d';
                    }
                    
                    const scorePercent = Math.round(chunk.contribution_score * 100);
                    const contentTypeDisplay = chunk.content_type ? chunk.content_type.replace('_', ' ').toLowerCase() : 'content';
                    
                    // Check if this chunk has LLM explanation
                    let llmExplanation = '';
                    let llmWasUsed = false;
                    
                    // Look for LLM metadata in the chunk data
                    if (list_of_chunks && list_of_chunks[filename]) {
                        Object.values(list_of_chunks[filename]).forEach(pageChunks => {
                            pageChunks.forEach(chunkData => {
                                if (chunkData.chunk_index === chunk.contribution_rank - 1) {
                                    llmExplanation = chunkData.llm_explanation || '';
                                    llmWasUsed = chunkData.llm_was_used || false;
                                }
                            });
                        });
                    }
                    
                    referencesToDisplay.push({
                        filename: filename,
                        displayInfo: `Page ${chunk.page_num} • ${impactLevel} (${scorePercent}% relevance)`,
                        chunkSpecificInfo: {
                            page_num: chunk.page_num,
                            content_type: contentTypeDisplay,
                            score: chunk.contribution_score,
                            rank: chunk.contribution_rank,
                            llm_explanation: llmExplanation,
                            llm_was_used: llmWasUsed
                        },
                        semanticScore: chunk.contribution_score,
                        impactColor: impactColor,
                        uniqueId: chunkId,
                        llmExplanation: llmExplanation,
                        llmWasUsed: llmWasUsed
                    });
                    
                    displayedChunks++;
                }
                
                console.log("📋 Final references to display:", referencesToDisplay);
                
                return {
                    filesToDisplay: referencesToDisplay,
                    title: `References (${referencesToDisplay.length} most relevant)`,
                    hasMoreCitations: false, // No longer showing "View All" button
                    hiddenCount: 0,
                    totalChunks: allChunks.length
                };
                
            } else {
                // Fallback to simple chunk counting when no semantic analysis
                console.log("📋 Using simple chunk count fallback for citation ranking");
                
                let allFileReferences = [];
                Object.keys(list_of_chunks).forEach(filename => {
                    let chunkCount = 0;
                    let pageCount = Object.keys(list_of_chunks[filename]).length;
                    Object.keys(list_of_chunks[filename]).forEach(pageNum => {
                        chunkCount += list_of_chunks[filename][pageNum].length;
                    });
                    allFileReferences.push({
                        filename: filename,
                        displayInfo: `${pageCount} page${pageCount > 1 ? 's' : ''} • ${chunkCount} reference${chunkCount > 1 ? 's' : ''}`,
                        chunkCount: chunkCount,
                        semanticScore: 0.5 // Default score for fallback
                    });
                });
                
                // Sort by chunk count
                allFileReferences.sort((a, b) => b.chunkCount - a.chunkCount);
                
                // Apply simple filtering logic - show top files with most citations
                let referencesToShow = allFileReferences;
                if (allFileReferences.length > 4) {
                    referencesToShow = allFileReferences.slice(0, 4);
                } else if (allFileReferences.length > 2) {
                    // Filter out files with very few chunks
                    const maxChunks = Math.max(...allFileReferences.map(f => f.chunkCount));
                    const threshold = Math.max(1, Math.ceil(maxChunks * 0.2));
                    referencesToShow = allFileReferences.filter(f => f.chunkCount >= threshold);
                    if (referencesToShow.length === 0) referencesToShow = allFileReferences.slice(0, 2);
                }
                
                return {
                    filesToDisplay: referencesToShow,
                    title: `References (${referencesToShow.length}${referencesToShow.length < totalFiles ? ` of ${totalFiles}` : ''})`,
                    hasMoreCitations: false, // No longer showing "View All" button
                    hiddenCount: 0
                };
            }
        }

        // =============================================================================
        // LLM REFERENCE ANALYSIS FUNCTIONS
        // =============================================================================
        

        
        // Helper function to safely handle individual chunk reference data
        function showPDFChunkReferenceById(chunkDataId) {
            console.log("📖 Loading chunk data for ID:", chunkDataId);
            
            if (!window.chunkDataStore || !window.chunkDataStore[chunkDataId]) {
                console.error("❌ Chunk data not found for ID:", chunkDataId);
                return;
            }
            
            const chunkInfo = window.chunkDataStore[chunkDataId];
            const encodedChunkData = JSON.stringify(chunkInfo.chunkData).replaceAll('"', "~>~<~");
            
            // Call the original function with safe data
            showPDFChunkReference(encodedChunkData, chunkInfo.filename);
            
            // Clean up the stored data to prevent memory leaks
            delete window.chunkDataStore[chunkDataId];
        }
        
        function showDetailedContributionAnalysis(contributionAnalysisData) {
            console.log("🤖 Show detailed reference analysis clicked");
            
            try {
                const cleanedData = contributionAnalysisData.replaceAll("~>~<~", '"');
                const analysis = JSON.parse(cleanedData);
                
                console.log("📊 Analysis data:", analysis);
                
                // Create detailed analysis modal
                const modalHtml = createDetailedAnalysisModal(analysis);
                
                // Remove existing modal if it exists
                const existingModal = document.getElementById('referenceAnalysisModal');
                if (existingModal) {
                    existingModal.remove();
                }
                
                // Add modal to body
                document.body.insertAdjacentHTML('beforeend', modalHtml);
                
                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('referenceAnalysisModal'));
                modal.show();
                
            } catch (error) {
                console.error("❌ Error showing detailed analysis:", error);
                alert("Error displaying detailed analysis. Please check the console.");
            }
        }
        
        function createDetailedAnalysisModal(analysis) {
            const summary = analysis.analysis_summary;
            const chunks = analysis.chunks_by_contribution;
            const analysisMethod = summary.analysis_method || 'unknown';
            const isLLMBased = analysisMethod === 'llm_based';
            
            return `
                <div class="modal fade" id="referenceAnalysisModal" tabindex="-1" aria-labelledby="referenceAnalysisModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content" style="background: #f8f9fa; border: none;">
                            <div class="modal-header" style="background: linear-gradient(135deg, ${isLLMBased ? '#4CAF50' : '#667eea'} 0%, ${isLLMBased ? '#2E7D32' : '#764ba2'} 100%); color: white; border-bottom: none;">
                                <h5 class="modal-title" id="referenceAnalysisModalLabel">
                                    <i class="fas fa-${isLLMBased ? 'robot' : 'brain'}" style="margin-right: 8px;"></i>
                                    ${isLLMBased ? 'Two-Stage LLM Reference Analysis' : 'Semantic Contribution Analysis'}
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body" style="padding: 20px;">
                                
                                <!-- Analysis Method Indicator -->
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <div class="alert ${isLLMBased ? 'alert-success' : 'alert-info'}" role="alert" style="margin-bottom: 20px; border: none; background: ${isLLMBased ? 'linear-gradient(135deg, #d4edda, #c3e6cb)' : 'linear-gradient(135deg, #cce7ff, #b3d9ff)'};">
                                            <div style="display: flex; align-items: center;">
                                                <i class="fas fa-${isLLMBased ? 'robot' : 'calculator'}" style="margin-right: 10px; font-size: 16px; color: ${isLLMBased ? '#155724' : '#0c5460'};"></i>
                                                <div>
                                                    <strong style="color: ${isLLMBased ? '#155724' : '#0c5460'};">
                                                        ${isLLMBased ? 'Two-Stage LLM Analysis' : 'Fallback Analysis'}
                                                    </strong>
                                                    <div style="font-size: 12px; color: ${isLLMBased ? '#155724' : '#0c5460'}; opacity: 0.8;">
                                                        ${isLLMBased ? 'References analyzed by AI for relevance and explanations provided' : 'Basic text-based relevance scoring used as fallback'}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Summary Statistics -->
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <h6 style="color: #495057; margin-bottom: 15px;">
                                            <i class="fas fa-chart-bar" style="margin-right: 6px;"></i>
                                            Analysis Summary
                                        </h6>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card border-0" style="background: #e8f5e8;">
                                            <div class="card-body text-center py-3">
                                                <div style="color: #28a745; font-size: 24px; font-weight: bold;">${summary.high_contribution_count}</div>
                                                <div style="color: #6c757d; font-size: 12px;">High Impact</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card border-0" style="background: #fff3cd;">
                                            <div class="card-body text-center py-3">
                                                <div style="color: #fd7e14; font-size: 24px; font-weight: bold;">${summary.medium_contribution_count}</div>
                                                <div style="color: #6c757d; font-size: 12px;">Medium Impact</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card border-0" style="background: #f8f9fa;">
                                            <div class="card-body text-center py-3">
                                                <div style="color: #6c757d; font-size: 24px; font-weight: bold;">${summary.low_contribution_count}</div>
                                                <div style="color: #6c757d; font-size: 12px;">Low Impact</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card border-0" style="background: #e3f2fd;">
                                            <div class="card-body text-center py-3">
                                                <div style="color: #1976d2; font-size: 24px; font-weight: bold;">${summary.average_score}</div>
                                                <div style="color: #6c757d; font-size: 12px;">Avg Score</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Detailed Chunks Analysis -->
                                <div class="row">
                                    ${chunks.high.length > 0 ? `
                                        <div class="col-md-12 mb-3">
                                            <h6 style="color: #28a745; margin-bottom: 10px;">
                                                <i class="fas fa-star" style="margin-right: 6px;"></i>
                                                High Impact Sources (${chunks.high.length})
                                            </h6>
                                            ${chunks.high.map((chunk, index) => `
                                                <div class="card mb-2 border-0" style="background: #e8f5e8; border-left: 4px solid #28a745 !important;">
                                                    <div class="card-body py-2 px-3">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <div>
                                                                <strong style="color: #495057; font-size: 14px;">${chunk.source.replace('.pdf', '')} - Page ${chunk.page_num}</strong>
                                                                <div style="color: #6c757d; font-size: 12px;">Rank: #${chunk.contribution_rank}</div>
                                                            </div>
                                                            <span class="badge" style="background: #28a745; color: white; font-size: 12px;">
                                                                ${Math.round(chunk.contribution_score * 100)}%
                                                            </span>
                                                        </div>
                                                        <div style="color: #6c757d; font-size: 11px; margin-top: 5px;">
                                                            Content Type: ${chunk.content_type || 'text'}
                                                        </div>
                                                        ${isLLMBased && chunk.llm_explanation && chunk.llm_explanation !== 'Not analyzed by LLM' ? `
                                                            <div style="color: #007bff; font-size: 11px; margin-top: 8px; font-style: italic; background: rgba(0,123,255,0.1); padding: 6px; border-radius: 4px; border-left: 3px solid #007bff;">
                                                                <i class="fas fa-robot" style="margin-right: 4px; font-size: 10px;"></i>
                                                                <strong>LLM Analysis:</strong> ${chunk.llm_explanation}
                                                            </div>
                                                        ` : ''}
                                                    </div>
                                                </div>
                                            `).join('')}
                                        </div>
                                    ` : ''}
                                    
                                    ${chunks.medium.length > 0 ? `
                                        <div class="col-md-12 mb-3">
                                            <h6 style="color: #fd7e14; margin-bottom: 10px;">
                                                <i class="fas fa-circle" style="margin-right: 6px;"></i>
                                                Medium Impact Sources (${chunks.medium.length})
                                            </h6>
                                            ${chunks.medium.slice(0, 5).map((chunk, index) => `
                                                <div class="card mb-2 border-0" style="background: #fff3cd; border-left: 4px solid #fd7e14 !important;">
                                                    <div class="card-body py-2 px-3">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <div>
                                                                <strong style="color: #495057; font-size: 14px;">${chunk.source.replace('.pdf', '')} - Page ${chunk.page_num}</strong>
                                                                <div style="color: #6c757d; font-size: 12px;">Rank: #${chunk.contribution_rank}</div>
                                                            </div>
                                                            <span class="badge" style="background: #fd7e14; color: white; font-size: 12px;">
                                                                ${Math.round(chunk.contribution_score * 100)}%
                                                            </span>
                                                        </div>
                                                        <div style="color: #6c757d; font-size: 11px; margin-top: 5px;">
                                                            Content Type: ${chunk.content_type || 'text'}
                                                        </div>
                                                    </div>
                                                </div>
                                            `).join('')}
                                            ${chunks.medium.length > 5 ? `<div style="color: #6c757d; font-size: 12px; text-align: center;">... and ${chunks.medium.length - 5} more</div>` : ''}
                                        </div>
                                    ` : ''}
                                    
                                    ${chunks.low.length > 0 ? `
                                        <div class="col-md-12">
                                            <h6 style="color: #6c757d; margin-bottom: 10px;">
                                                <i class="fas fa-minus-circle" style="margin-right: 6px;"></i>
                                                Low Impact Sources (${chunks.low.length})
                                            </h6>
                                            <div style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; color: #6c757d; font-size: 12px;">
                                                ${chunks.low.length} sources with lower semantic contribution scores
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>
                                
                            </div>
                            <div class="modal-footer" style="border-top: 1px solid #dee2e6;">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // =============================================================================
        // END SEMANTIC CONTRIBUTION ANALYSIS FUNCTIONS  
        // =============================================================================

    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>